-- Create game_content table if it doesn't exist
CREATE TABLE IF NOT EXISTS `game_content` (
  `content_id` int(11) NOT NULL AUTO_INCREMENT,
  `level_number` int(11) NOT NULL,
  `question_text` text NOT NULL,
  `option1` varchar(500) NOT NULL,
  `option2` varchar(500) NOT NULL,
  `option3` varchar(500) NOT NULL,
  `option4` varchar(500) NOT NULL,
  `correct_answer` varchar(500) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`content_id`),
  KEY `level_number` (`level_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert sample matching questions for level 2 (matching quiz)
-- For matching questions:
-- option1 = first term, option2 = first definition
-- option3 = second term, option4 = second definition
-- correct_answer is not used for matching (validation is done by pairing)

INSERT INTO `game_content` (`level_number`, `question_text`, `option1`, `option2`, `option3`, `option4`, `correct_answer`) VALUES
(2, 'Match the programming concepts with their definitions:', 'Variable', 'A storage location with an associated name', 'Function', 'A reusable block of code that performs a specific task', 'matching'),
(2, 'Match the data types with their examples:', 'String', '"Hello World"', 'Integer', '42', 'matching'),
(2, 'Match the HTML elements with their purposes:', 'div', 'A generic container element', 'span', 'An inline container element', 'matching'),
(2, 'Match the CSS properties with their functions:', 'color', 'Sets the text color', 'background-color', 'Sets the background color', 'matching'),
(2, 'Match the JavaScript methods with their actions:', 'push()', 'Adds an element to the end of an array', 'pop()', 'Removes the last element from an array', 'matching'),
(2, 'Match the database terms with their meanings:', 'Primary Key', 'A unique identifier for a record', 'Foreign Key', 'A reference to a primary key in another table', 'matching'),
(2, 'Match the network protocols with their purposes:', 'HTTP', 'Protocol for transferring web pages', 'FTP', 'Protocol for transferring files', 'matching'),
(2, 'Match the software development terms with their definitions:', 'Bug', 'An error or flaw in software', 'Debug', 'The process of finding and fixing bugs', 'matching'),
(2, 'Match the computer components with their functions:', 'CPU', 'Processes instructions and calculations', 'RAM', 'Temporary storage for active programs', 'matching'),
(2, 'Match the programming paradigms with their characteristics:', 'Object-Oriented', 'Organizes code into objects with properties and methods', 'Functional', 'Treats computation as evaluation of functions', 'matching'),
(2, 'Match the web technologies with their roles:', 'HTML', 'Structures the content of web pages', 'CSS', 'Styles the appearance of web pages', 'matching'),
(2, 'Match the version control terms with their meanings:', 'Commit', 'Save changes to the repository', 'Branch', 'A separate line of development', 'matching'),
(2, 'Match the algorithm types with their characteristics:', 'Sorting', 'Arranges data in a specific order', 'Searching', 'Finds specific data within a collection', 'matching'),
(2, 'Match the security concepts with their purposes:', 'Encryption', 'Converts data into a coded format', 'Authentication', 'Verifies the identity of a user', 'matching'),
(2, 'Match the cloud computing terms with their definitions:', 'SaaS', 'Software as a Service - applications delivered over the internet', 'IaaS', 'Infrastructure as a Service - virtualized computing resources', 'matching');
