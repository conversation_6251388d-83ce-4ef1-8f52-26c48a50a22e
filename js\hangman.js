// Game State
let currentQuestionIndex = 0;
let wrongAnswers = 0;
let timer;
let timeLeft = 30;
let selectedAnswer = null;
let gameActive = false;
let gameData = []; // Store the questions from database
let isTimeFrozen = false; // Track if time is frozen
let powerUpsUsed = {
    addTime: false,
    eliminateWrong: 0, // Track number of times used (0-2)
    showAnswer: false
}; // Track which power-ups have been used
let frozenTimer = null; // Store the frozen timer state
let totalLives = 1; // Will be set dynamically in initGame
let totalExpEarned = 0; // Track total EXP earned in this session

// DOM Elements
const questionElement = document.getElementById('question');
const optionsElement = document.getElementById('options');
const nextButton = document.getElementById('next-btn');
const timeElement = document.getElementById('time');
const livesElement = document.getElementById('lives');
const robotElement = document.getElementById('robot');
const gameArea = document.getElementById('game-area');
const instructionModal = document.getElementById('instruction-modal');
const startButton = document.getElementById('start-btn');
const resultsModal = document.getElementById('results-modal');
const resultTitle = document.getElementById('result-title');
const resultStars = document.getElementById('result-stars');
const resultMessage = document.getElementById('result-message');
const restartButton = document.getElementById('restart-btn');
const volumeSlider = document.getElementById('volume-slider');
const volumeValue = document.getElementById('volume-value');

// Power-up buttons
const addTimeBtn = document.getElementById('addTimeBtn');
const eliminateWrongBtn = document.getElementById('eliminate-wrong');
const showAnswerBtn = document.getElementById('show-answer');

// Sound Effects
const correctSound = new Audio('../../sounds/correct2.mp3');
const wrongSound = new Audio('../../sounds/wrong2.mp3');
const breakSound = new Audio('../../sounds/break2.mp3');
const successSound = new Audio('../../sounds/success.mp3');

// Set initial volume for all sound effects
correctSound.volume = 0.3;
wrongSound.volume = 0.3;
breakSound.volume = 0.3;
successSound.volume = 0.3;

// Power-up Functions
function addTime() {
    if (powerUpsUsed.addTime) return;
    
    // Add 10 seconds to the timer
    timeLeft += 10;
    timeElement.textContent = timeLeft;
    timeElement.classList.add('time-added');
    
    // Visual feedback
    setTimeout(() => {
        timeElement.classList.remove('time-added');
    }, 500);
    
    // Update button state
    powerUpsUsed.addTime = true;
    addTimeBtn.disabled = true;
    
    // Update button text
    addTimeBtn.innerHTML = '<span class="power-up-icon">⏱️</span><span class="power-up-text">+10 Used</span>';
    
    // Play sound effect (if available)
    // playSound(addTimeSound);
}

function eliminateWrongAnswer() {
    // Check if we've used this power-up twice already
    if (powerUpsUsed.eliminateWrong >= 2 || selectedAnswer !== null) return;
    
    const currentQuestion = gameData[currentQuestionIndex];
    const options = document.querySelectorAll('.option');
    let wrongOptions = [];
    const correctValue = currentQuestion._correctValue;
    // Find all wrong and enabled options
    options.forEach((option, index) => {
        if (currentQuestion._shuffledOptions[index] !== correctValue && !option.disabled) {
            wrongOptions.push({
                element: option,
                index: index
            });
        }
    });
    
    // If there are wrong options to eliminate
    if (wrongOptions.length > 0) {
        // Randomly select one wrong option to eliminate
        const optionToEliminate = Math.floor(Math.random() * wrongOptions.length);
        const option = wrongOptions[optionToEliminate].element;
        
        // Disable the selected wrong option
        option.style.opacity = '0.3';
        option.style.pointerEvents = 'none';
        option.style.transform = 'scale(0.95)';
        option.style.transition = 'all 0.3s ease';
        option.disabled = true;
        
        // Update usage count
        powerUpsUsed.eliminateWrong++;
        
        // Update button text to show remaining uses
        const remainingUses = 2 - powerUpsUsed.eliminateWrong;
        const originalText = eliminateWrongBtn.innerHTML;
        
        if (remainingUses > 0) {
            eliminateWrongBtn.innerHTML = `
                <span class="power-up-icon">❌</span>
                <span class="power-up-text">Eliminate 1 (${remainingUses} left)</span>`;
        } else {
            eliminateWrongBtn.disabled = true;
            eliminateWrongBtn.innerHTML = '<span class="power-up-icon">✅</span><span class="power-up-text">Used Up!</span>';
        }
        
        // Add elimination effect
        option.animate([
            { opacity: 1, transform: 'scale(1)' },
            { opacity: 0.3, transform: 'scale(0.95)' }
        ], {
            duration: 500,
            easing: 'ease-out'
        });
        
        // Reset button after animation if no uses left
        if (remainingUses === 0) {
            setTimeout(() => {
                eliminateWrongBtn.innerHTML = '<span class="power-up-icon">❌</span><span class="power-up-text">Used Up!</span>';
            }, 1500);
        }
    }
}

function showAnswer() {
    if (powerUpsUsed.showAnswer || selectedAnswer !== null) return;
    const currentQuestion = gameData[currentQuestionIndex];
    const options = document.querySelectorAll('.option');
    const correctValue = currentQuestion._correctValue;
    // Find the index of the correct answer in the shuffled options
    const correctIndex = currentQuestion._shuffledOptions.findIndex(opt => opt === correctValue);
    if (correctIndex !== -1) {
        options[correctIndex].classList.add('correct-answer');
        // Disable the power-up
        powerUpsUsed.showAnswer = true;
        showAnswerBtn.disabled = true;
        // Add visual feedback
        const originalText = showAnswerBtn.innerHTML;
        showAnswerBtn.innerHTML = '<span class="power-up-icon">✨</span><span class="power-up-text">Revealed!</span>';
        // Remove highlight after 1.5 seconds
        setTimeout(() => {
            options[correctIndex].classList.remove('correct-answer');
            showAnswerBtn.innerHTML = originalText;
        }, 200);
    }
}

// Fisher-Yates shuffle for arrays
function shuffle(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
}

// Initialize Game
async function initGame() {
    try {
        // Get the level from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const level = parseInt(urlParams.get('level')) || 1; // Default to level 1 if not set

        console.log('Current URL:', window.location.href); // Debug log
        console.log('Level parameter from URL:', urlParams.get('level')); // Debug log
        console.log('Parsed level:', level); // Debug log

        // Fetch questions from the database
        const response = await fetch(`../../php/game.php?level=${level}`);
        const result = await response.json();
        
        console.log('Fetched data:', result); // Debug log
        
        if (result.success) {
            gameData = result.data;
            // Shuffle the questions
            shuffle(gameData);
            console.log('Game data loaded:', gameData); // Debug log
            
            if (gameData.length === 0) {
                console.error('No questions found in the database');
                alert('No questions found. Please try again later.');
                return;
            }
            
            currentQuestionIndex = 0;
            wrongAnswers = 0;
            gameActive = true;
            // Set totalLives to 20% of total questions, minimum 1, using Math.floor
            totalLives = Math.max(1, Math.ceil(gameData.length * 0.2));
            livesElement.textContent = totalLives - wrongAnswers;
            resetRobotPosition();
            resetState();
            showQuestion();
            startTimer();
        } else {
            console.error('Failed to load game data:', result.error);
            alert('Failed to load game data. Please try again later.');
        }
    } catch (error) {
        console.error('Error loading game data:', error);
        alert('Error loading game data. Please try again later.');
    }
}

// Show Current Question
function showQuestion() {
    if (!gameActive) return; // Don't proceed if game is not active
    
    if (currentQuestionIndex >= gameData.length) {
        endGame(true);
        return;
    }

    resetState();
    const currentQuestion = gameData[currentQuestionIndex];
    console.log('Current question:', currentQuestion); // Debug log
    
    if (!currentQuestion || !currentQuestion.question_text) {
        console.error('Invalid question data:', currentQuestion);
        return;
    }

    questionElement.textContent = currentQuestion.question_text;

    // Create options from the database fields
    let options = [
        currentQuestion.option1,
        currentQuestion.option2,
        currentQuestion.option3,
        currentQuestion.option4
    ];
    // Shuffle the options
    options = shuffle(options);
    // Store the shuffled options for this question
    currentQuestion._shuffledOptions = options;
    // Store the correct answer value for this question
    currentQuestion._correctValue = currentQuestion.correct_answer;
    // Clear previous options
    optionsElement.innerHTML = '';
    options.forEach((optionValue, index) => {
        if (!optionValue) {
            console.error('Missing option at index:', index);
            return;
        }
        const button = document.createElement('button');
        button.className = 'option';
        button.textContent = optionValue;
        button.addEventListener('click', () => selectAnswer(index));
        optionsElement.appendChild(button);
    });
}

// Reset Question State
function resetState() {
    selectedAnswer = null;
    timeLeft = 15; // Changed from 30 to 15 to match the timer start value
    timeElement.textContent = timeLeft;
    nextButton.disabled = true;
    optionsElement.innerHTML = '';
    
    if (powerUpsUsed.eliminateWrong < 2) eliminateWrongBtn.disabled = false;
    if (!powerUpsUsed.showAnswer) showAnswerBtn.disabled = false;
    
    // Update eliminate button text to show remaining uses
    if (powerUpsUsed.eliminateWrong > 0 && powerUpsUsed.eliminateWrong < 2) {
        const remainingUses = 2 - powerUpsUsed.eliminateWrong;
        eliminateWrongBtn.innerHTML = `
            <span class="power-up-icon">❌</span>
            <span class="power-up-text">Eliminate 1 (${remainingUses} left)</span>`;
    }

}

// Select Answer
function selectAnswer(index) {
    if (!gameActive || selectedAnswer !== null) return;

    selectedAnswer = index;
    const options = document.querySelectorAll('.option');
    
    // Highlight selected answer
    options.forEach((option, i) => {
        if (i === index) {
            option.classList.add('selected');
        }
        option.disabled = true;
    });
    
    nextButton.disabled = false;
    clearInterval(timer);

    const currentQuestion = gameData[currentQuestionIndex];
    const selectedValue = currentQuestion._shuffledOptions[index];
    const correctValue = currentQuestion._correctValue;
    if (selectedValue !== correctValue) {
        wrongAnswers++;
        livesElement.textContent = totalLives - wrongAnswers;
        lowerRobot();
        // Play wrong answer sound
        playSound(wrongSound);
        // Check if game is over due to too many wrong answers
        if (wrongAnswers >= totalLives) {
            makeRobotDie();
        }
    } else {
        // Play correct answer sound
        playSound(correctSound);
        // Increase user exp by 10 for correct answer
        const expEarned = 10;
        totalExpEarned += expEarned;
        // Send exp data to savetodb.php
        fetch('../../php/savetodb.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                expEarned: expEarned,
                score: 0, // We'll update the final score at the end
                starsCount: 0, // We'll update the stars at the end
                level: parseInt(new URLSearchParams(window.location.search).get('level')) || 1 // Get level from URL parameters
            })
        })
        .then(response => response.json())
        .then(data => {
            if (!data.success) {
                console.error('Failed to save exp:', data.error);
            }
        })
        .catch(error => {
            console.error('Error saving exp:', error);
        });
    }
}

// Lower Robot
function lowerRobot() {
    const currentTop = parseInt(robotElement.style.top || '70');
    const newTop = currentTop + 40;
    
    // Get elements
    const ropeElement = document.querySelector('.rope');
    const ropeAroundNeck = document.querySelector('.rope-around-neck');
    
    // Calculate new rope length based on robot position
    const ropeLength = 40 + (newTop - 70);
    
    // Apply smooth transitions
    robotElement.style.transition = 'top 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
    ropeElement.style.transition = 'height 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
    ropeAroundNeck.style.transition = 'transform 0.3s ease-in-out';
    
    // Set new positions
    robotElement.style.top = newTop + 'px';
    ropeElement.style.height = ropeLength + 'px';
    
    // Add a slight swing effect to the robot
    robotElement.style.animation = 'none';
    setTimeout(() => {
        robotElement.style.animation = 'sway 2s ease-in-out infinite';
    }, 800); // Wait for the transition to complete
    
    // Add a visual effect to the rope-around-neck
    ropeAroundNeck.style.transform = 'translateX(-50%) scale(1.1)';
    setTimeout(() => {
        ropeAroundNeck.style.transform = 'translateX(-50%) scale(1)';
    }, 300);

    if (wrongAnswers >= totalLives) {
        makeRobotDie();
    }
}

// Make Robot Die Dramatically
function makeRobotDie() {
    // Immediately disable game controls and prevent any other game completion
    gameActive = false;
    clearInterval(timer);
    
    // Add dramatic dying animation
    robotElement.style.animation = 'none';
    robotElement.style.transition = 'all 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
    
    // First, make the robot swing back and forth
    robotElement.style.transform = 'rotate(-15deg)';
    
    // Change robot's appearance to look "dead"
    const eyes = document.querySelectorAll('.eye');
    eyes.forEach(eye => {
        eye.style.background = 'linear-gradient(145deg, #e0e0e0, #bdbdbd)';
        const pupil = eye.querySelector('.pupil');
        if (pupil) {
            pupil.style.height = '2px';
            pupil.style.top = '8px';
            pupil.style.transform = 'rotate(90deg)';
        }
    });
    
    // Change mouth to look sad
    const mouth = document.querySelector('.mouth');
    if (mouth) {
        mouth.style.height = '5px';
        mouth.style.width = '20px';
        mouth.style.borderRadius = '0 0 10px 10px';
        mouth.style.background = 'linear-gradient(to bottom, #b71c1c, #7f0000)';
    }
    
    // Turn off the antenna light
    const antenna = document.querySelector('.antenna::after');
    if (antenna) {
        antenna.style.background = '#9e9e9e';
        antenna.style.boxShadow = 'none';
    }
    
    // Turn off the buttons
    const buttons = document.querySelectorAll('.button');
    buttons.forEach(button => {
        button.style.background = '#9e9e9e';
        button.style.boxShadow = 'none';
        button.style.animation = 'none';
    });
    
    setTimeout(() => {
        robotElement.style.transform = 'rotate(15deg)';
        
        // After swinging, make the robot fall
        setTimeout(() => {
            // Break the rope effect
            const ropeElement = document.querySelector('.rope');
            ropeElement.style.height = '400px';
            ropeElement.style.opacity = '0.5';
            
            // Make the robot fall
            robotElement.style.top = '350px';
            robotElement.style.transform = 'rotate(90deg)';
            
            // Add a visual effect to the rope-around-neck
            const ropeAroundNeck = document.querySelector('.rope-around-neck');
            ropeAroundNeck.style.transform = 'translateX(-50%) scale(1.2)';
            
            // Add a "breaking" sound effect
            playSound(breakSound);
            
            // Show game over after the fall
            setTimeout(() => {
                endGame(false);
            }, 1000);
        }, 500);
    }, 300);
}

// Play Sound Effect
function playSound(sound) {
    // Reset the sound to the beginning
    sound.currentTime = 0;
    // Play the sound
    sound.play().catch(e => console.log('Audio play failed:', e));
}

// Reset Robot Position
function resetRobotPosition() {
    robotElement.style.top = '70px';
    robotElement.style.transform = 'rotate(0deg)';
    
    // Reset rope length
    const ropeElement = document.querySelector('.rope');
    ropeElement.style.height = '40px';
    ropeElement.style.opacity = '1';
    
    // Reset rope-around-neck
    const ropeAroundNeck = document.querySelector('.rope-around-neck');
    ropeAroundNeck.style.transform = 'translateX(-50%) scale(1)';
    
    // Reset robot appearance
    resetRobotAppearance();
    
    // Reset animation
    robotElement.style.animation = 'sway 4s ease-in-out infinite';
}

// Reset Robot Appearance
function resetRobotAppearance() {
    // Reset eyes
    const eyes = document.querySelectorAll('.eye');
    eyes.forEach(eye => {
        eye.style.background = 'linear-gradient(145deg, #ffffff, #e6e6e6)';
        const pupil = eye.querySelector('.pupil');
        if (pupil) {
            pupil.style.height = '8px';
            pupil.style.top = '4px';
            pupil.style.transform = 'none';
        }
    });
    
    // Reset mouth
    const mouth = document.querySelector('.mouth');
    if (mouth) {
        mouth.style.height = '10px';
        mouth.style.width = '30px';
        mouth.style.borderRadius = '5px';
        mouth.style.background = 'linear-gradient(to bottom, #e53935, #c62828)';
    }
    
    // Reset antenna light
    const antenna = document.querySelector('.antenna::after');
    if (antenna) {
        antenna.style.background = '#f44336';
        antenna.style.boxShadow = '0 0 10px #ff6659';
    }
    
    // Reset buttons
    const buttons = document.querySelectorAll('.button');
    buttons.forEach((button, index) => {
        button.style.background = index === 0 ? '#f44336' : (index === 1 ? '#ffeb3b' : '#4caf50');
        button.style.boxShadow = 'inset 0 -2px 3px rgba(0, 0, 0, 0.3), 0 2px 3px rgba(0, 0, 0, 0.3)';
        button.style.animation = 'glow 2s infinite alternate';
        button.style.animationDelay = `${index * 0.5}s`;
    });
}

// Next Question
function nextQuestion() {
    if (!gameActive) return; // Don't proceed if game is not active
    
    if (currentQuestionIndex < gameData.length - 1) {
        currentQuestionIndex++;
        showQuestion();
        startTimer();
    } else {
        endGame(true);
    }
}

// Timer
function startTimer() {
    // Don't start a new timer if time is frozen
    if (isTimeFrozen) return;
    
    clearInterval(timer);
    timeLeft = 15;
    timeElement.textContent = timeLeft;

    timer = setInterval(() => {
        // Skip timer update if time is frozen
        if (isTimeFrozen) return;
        
        timeLeft--;
        timeElement.textContent = timeLeft;

        if (timeLeft <= 0) {
            clearInterval(timer);
            if (selectedAnswer === null) {
                wrongAnswers++;
                livesElement.textContent = totalLives - wrongAnswers;
                lowerRobot();
                document.querySelectorAll('.option').forEach(opt => opt.disabled = true);
                nextButton.disabled = false;
                
                // Check if game is over due to too many wrong answers
                if (wrongAnswers >= totalLives) {
                    // The makeRobotDie function will call endGame(false)
                    makeRobotDie();
                }
            }
        }
    }, 1000);
}

// End Game
function endGame(success) {
    gameActive = false;
    clearInterval(timer);

    if (!success) {
        // Robot has already died in makeRobotDie function
        showResults(false);
    } else {
        showResults(true);
    }
}

// Show Results with Star Rating
function showResults(success) {
    // Create results modal if it doesn't exist
    let resultsModal = document.getElementById('results-modal');
    if (!resultsModal) return;

    const totalQuestions = gameData.length;
    const correctAnswers = totalQuestions - wrongAnswers;
    const percentage = Math.round((correctAnswers / totalQuestions) * 100);

    // Calculate stars (0-3)
    let stars = 0;
    if (percentage >= 96) stars = 3;
    else if (percentage >= 86) stars = 2;
    else if (percentage >= 80) stars = 1;

    // Update modal content
    document.getElementById('result-title').textContent = success
        ? (percentage === 100 ? '🎉 PERFECT! 🎉' : 'Congratulations!')
        : 'Game Over!';

    // Display stars
    const resultStars = document.getElementById('result-stars');
    resultStars.innerHTML = '';
    for (let i = 0; i < 3; i++) {
        const star = document.createElement('span');
        star.className = i < stars ? 'star' : 'star empty';
        star.textContent = i < stars ? '⭐' : '☆';
        resultStars.appendChild(star);
    }

    // Set message
    document.getElementById('result-message').textContent = success
        ? `You answered ${correctAnswers} out of ${totalQuestions} questions correctly!`
        : 'You leave with 0 points.';

    // Update exp display with the actual total earned
    const expText = document.querySelector('.exp-text');
    if (expText) expText.textContent = `+${totalExpEarned} EXP`;

    // Get current level from URL
    const level = parseInt(new URLSearchParams(window.location.search).get('level')) || 1;

    // Fetch previous best stars for this level and only update if new stars is higher
    fetch('../../php/get_user_levels.php')
        .then(response => response.json())
        .then(data => {
            let previousStars = 0;
            if (data.success && Array.isArray(data.levels)) {
                const levelData = data.levels.find(l => parseInt(l.level_number) === level);
                if (levelData && levelData.levelStar) {
                    previousStars = parseInt(levelData.levelStar) || 0;
                }
            }
            // Only send to DB if new stars is higher than previous
            if (stars > previousStars) {
                fetch('../../php/savetodb.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        expEarned: 0, // We already sent exp per question
                        score: correctAnswers,
                        starsCount: stars,
                        level: level
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (!data.success) {
                        console.error('Failed to save game results:', data.error);
                    }
                })
                .catch(error => {
                    console.error('Error saving game results:', error);
                });
            } else {
                // Still update score if needed, but keep the higher star value
                fetch('../../php/savetodb.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        expEarned: 0,
                        score: correctAnswers,
                        starsCount: previousStars, // keep previous best
                        level: level
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (!data.success) {
                        console.error('Failed to save game results:', data.error);
                    }
                })
                .catch(error => {
                    console.error('Error saving game results:', error);
                });
            }
        })
        .catch(error => {
            console.error('Error fetching previous stars:', error);
        });

    // Update buttons based on success/failure
    const buttonContainer = document.querySelector('.modal-buttons');
    buttonContainer.innerHTML = '';

    // Create Play Again button
    const playAgainBtn = document.createElement('button');
    playAgainBtn.textContent = 'PLAY AGAIN';
    playAgainBtn.className = 'replay-btn';
    playAgainBtn.id = 'play-again-btn';
    playAgainBtn.addEventListener('click', () => {
        resultsModal.style.display = 'none';
        pullRobotBack();
    });
    buttonContainer.appendChild(playAgainBtn);

    // Create Main Menu button
    const mainMenuBtn = document.createElement('button');
    mainMenuBtn.textContent = 'MAIN MENU';
    mainMenuBtn.className = 'main-menu-btn';
    mainMenuBtn.id = 'back-to-menu-btn';
    mainMenuBtn.addEventListener('click', () => {
        window.location.href = '../../mainpage.html';
    });
    buttonContainer.appendChild(mainMenuBtn);

    // Show the modal
    resultsModal.style.display = 'flex';
}

// Create snowflakes for freeze effect
// Removed unused freeze time visual effect functions

// Event Listeners for Power-ups
addTimeBtn.addEventListener('click', addTime);
eliminateWrongBtn.addEventListener('click', eliminateWrongAnswer);
showAnswerBtn.addEventListener('click', showAnswer);

// Event Listeners
startButton.addEventListener('click', () => {
    instructionModal.style.display = 'none';
    gameArea.style.display = 'flex';
    initGame();
});

nextButton.addEventListener('click', nextQuestion);

// Check if restartButton exists before adding event listener
if (restartButton) {
    restartButton.addEventListener('click', () => {
        resultsModal.style.display = 'none';
        pullRobotBack();
    });
}

// Volume Control Event Listener
volumeSlider.addEventListener('input', () => {
    const volume = volumeSlider.value / 100;
    volumeValue.textContent = `${volumeSlider.value}%`;
    
    // Update all sound volumes
    correctSound.volume = volume;
    wrongSound.volume = volume;
    breakSound.volume = volume;
    successSound.volume = volume;
    
    // Update speaker icon based on volume
    updateSpeakerIcon(volume);
    
    // Play a test sound if volume is not zero
    if (volume > 0) {
        // Create a temporary sound for testing
        const testSound = new Audio('../../sounds/correct2.mp3');
        testSound.volume = volume;
        testSound.play().catch(e => console.log('Test sound play failed:', e));
    }
});

// Add mute/unmute functionality
const volumeLabel = document.querySelector('.volume-control label');
let previousVolume = 30; // Default volume

volumeLabel.addEventListener('click', () => {
    if (volumeSlider.value > 0) {
        // Store current volume and mute
        previousVolume = parseInt(volumeSlider.value);
        volumeSlider.value = 0;
        volumeValue.textContent = '0%';
        
        // Update all sound volumes
        correctSound.volume = 0;
        wrongSound.volume = 0;
        breakSound.volume = 0;
        successSound.volume = 0;
        
        // Update speaker icon
        updateSpeakerIcon(0);
    } else {
        // Restore previous volume
        volumeSlider.value = previousVolume;
        volumeValue.textContent = `${previousVolume}%`;
        
        // Update all sound volumes
        const volume = previousVolume / 100;
        correctSound.volume = volume;
        wrongSound.volume = volume;
        breakSound.volume = volume;
        successSound.volume = volume;
        
        // Update speaker icon
        updateSpeakerIcon(volume);
    }
});

// Function to update speaker icon based on volume
function updateSpeakerIcon(volume) {
    if (volume === 0) {
        volumeLabel.textContent = '🔇';
    } else if (volume < 0.3) {
        volumeLabel.textContent = '🔈';
    } else if (volume < 0.7) {
        volumeLabel.textContent = '🔉';
    } else {
        volumeLabel.textContent = '🔊';
    }
}

// Pull Robot Back Up
function pullRobotBack() {
    // First, make the robot appear to be pulled back up
    const robotElement = document.getElementById('robot');
    const ropeElement = document.querySelector('.rope');
    const ropeAroundNeck = document.querySelector('.rope-around-neck');
    
    // Reset robot appearance first
    resetRobotAppearance();
    
    // Add pulling animation
    robotElement.style.transition = 'all 1.5s cubic-bezier(0.34, 1.56, 0.64, 1)';
    robotElement.style.top = '70px';
    robotElement.style.transform = 'rotate(0deg)';
    
    // Animate the rope shortening
    ropeElement.style.transition = 'all 1.5s cubic-bezier(0.34, 1.56, 0.64, 1)';
    ropeElement.style.height = '40px';
    ropeElement.style.opacity = '1';
    
    // Reset rope-around-neck
    ropeAroundNeck.style.transition = 'all 1.5s cubic-bezier(0.34, 1.56, 0.64, 1)';
    ropeAroundNeck.style.transform = 'translateX(-50%) scale(1)';
    
    // Add a slight bounce effect when the robot reaches the top
    setTimeout(() => {
        robotElement.style.transition = 'all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)';
        robotElement.style.top = '65px';
        
        setTimeout(() => {
            robotElement.style.top = '70px';
            
            // Start the normal sway animation after the bounce
            setTimeout(() => {
                robotElement.style.animation = 'sway 4s ease-in-out infinite';
                robotElement.style.transition = 'none';
                
                // Initialize the game after the animation completes
                initGame();
            }, 300);
        }, 150);
    }, 1500);
}

// Start with instructions
window.addEventListener('DOMContentLoaded', () => {
    instructionModal.style.display = 'flex';
    gameArea.style.display = 'none';
    // resultsModal.style.display = 'none';
    addCelebrationAnimation();
});

// Add this to your CSS or create a new style element
function addCelebrationAnimation() {
    const style = document.createElement('style');
    style.textContent = `
        @keyframes celebrate {
            0%, 100% { transform: rotate(0deg) translateY(0); }
            25% { transform: rotate(-5deg) translateY(-5px); }
            75% { transform: rotate(5deg) translateY(-5px); }
        }
        
        @keyframes fall {
            0% { transform: rotate(0deg); }
            20% { transform: rotate(-15deg); }
            40% { transform: rotate(15deg); }
            60% { transform: rotate(-10deg); }
            80% { transform: rotate(10deg); }
            100% { transform: rotate(90deg); }
        }
        
        @keyframes die {
            0% { transform: rotate(0deg); }
            20% { transform: rotate(-15deg); }
            40% { transform: rotate(15deg); }
            60% { transform: rotate(-10deg); }
            80% { transform: rotate(10deg); }
            100% { transform: rotate(90deg); }
        }
        
        @keyframes pullBack {
            0% { transform: rotate(90deg); }
            100% { transform: rotate(0deg); }
        }
    `;
    document.head.appendChild(style);
}
