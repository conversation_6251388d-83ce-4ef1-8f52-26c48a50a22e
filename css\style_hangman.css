/* Base Styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #0b0b23;
    color: #fff;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding: 10px;
    overflow-x: hidden;
    overflow-y: auto;
}

/* Game Container */
.game-container {
    width: 100%;
    max-width: 1200px;
    min-height: 100vh;
    position: relative;
    display: flex;
    flex-direction: column;
    margin: 0 auto;
}

/* Modals */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
}

.modal-content {
    background: linear-gradient(145deg, #1a1a3a, #0f0f2a);
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 0 30px rgba(0, 150, 255, 0.3);
    border: 1px solid rgba(79, 195, 247, 0.2);
    animation: modalAppear 0.5s ease-out;
}

@keyframes modalAppear {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

#result-title {
    color: #4fc3f7;
    font-size: 2.5rem;
    margin-bottom: 20px;
    text-shadow: 0 0 10px rgba(79, 195, 247, 0.5);
}

.stars {
    font-size: 2.5rem;
    margin: 20px 0;
    letter-spacing: 10px;
}

#result-message {
    color: #fff;
    font-size: 1.2rem;
    margin: 20px 0;
    line-height: 1.5;
}

.exp-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin: 20px 0;
    padding: 15px;
    background: rgba(79, 195, 247, 0.1);
    border-radius: 12px;
    border: 1px solid rgba(79, 195, 247, 0.2);
    animation: expPulse 2s infinite;
}

@keyframes expPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 15px rgba(79, 195, 247, 0.2);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 25px rgba(79, 195, 247, 0.4);
    }
}

.exp-icon {
    font-size: 1.8rem;
    animation: sparkle 1.5s infinite;
}

@keyframes sparkle {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
    50% {
        transform: scale(1.2) rotate(15deg);
        opacity: 0.8;
    }
}

.exp-text {
    color: #ffeb3b;
    font-size: 1.5rem;
    font-weight: bold;
    text-shadow: 0 0 10px rgba(255, 235, 59, 0.5);
}

.modal-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 30px;
}

#start-btn, .next-btn, .continue-btn, .try-again-btn, .exit-btn, .power-up-btn {
    border-radius: 12px;
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    min-width: 150px;
    padding: 15px 30px;
}

#start-btn, .continue-btn {
    background: linear-gradient(145deg, #4caf50, #388e3c);
    color: white;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

#start-btn:hover, .continue-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.exit-btn {
    background: linear-gradient(145deg, #f44336, #d32f2f);
    color: white;
    box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
}

.exit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
}

.try-again-btn {
    background: linear-gradient(145deg, #ffd700, #ffed4e);
    color: #1a1a2e;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.try-again-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
}

/* Responsive styles for modal */
@media (max-width: 480px) {
    .modal-content {
        padding: 20px;
    }

    #result-title {
        font-size: 2rem;
    }

    .stars {
        font-size: 2rem;
    }

    #result-message {
        font-size: 1rem;
    }

    .exp-container {
        padding: 10px;
    }

    .exp-icon {
        font-size: 1.5rem;
    }

    .exp-text {
        font-size: 1.2rem;
    }

    .modal-buttons {
        flex-direction: column;
        gap: 15px;
    }

    #start-btn, .next-btn, .continue-btn, .try-again-btn, .exit-btn, .power-up-btn {
        width: 100%;
        padding: 12px 20px;
        font-size: 1.1rem;
    }
}

/* Game Area */
.game-area {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    gap: 15px;
    padding: 10px 0;
}

.quiz-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding: 15px;
    overflow-y: visible;
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(145deg, #1a1a3a, #0f0f2a);
    padding: 12px 20px;
    border-radius: 12px;
    border: 1px solid rgba(79, 195, 247, 0.2);
    box-shadow: 0 0 15px rgba(0, 150, 255, 0.1);
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.header .timer, .header .lives {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1.1rem;
    font-weight: bold;
    color: #4fc3f7;
    text-shadow: 0 0 10px rgba(79, 195, 247, 0.3);
}

.header .lives {
    color: #ff6b6b;
    text-shadow: 0 0 10px rgba(255, 107, 107, 0.3);
}

.question-counter {
    color: #fff;
    font-size: 1rem;
    font-weight: bold;
}

.volume-control {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #4fc3f7;
    font-size: 0.9rem;
}

/* Question */
.question {
    font-size: 1.2rem;
    color: #fff;
    margin-bottom: 15px;
    line-height: 1.4;
    text-align: center;
    background: linear-gradient(145deg, #1a1a3a, #0f0f2a);
    padding: 15px;
    border-radius: 12px;
    border: 1px solid rgba(79, 195, 247, 0.2);
    box-shadow: 0 0 15px rgba(0, 150, 255, 0.1);
}

/* Power-ups */
.power-ups {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin: 15px 0;
    flex-wrap: wrap;
}

.power-up-btn {
    background: linear-gradient(145deg, #4fc3f7, #3fb3e7);
    color: #0b0b23;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 3px 0 #2fa3d7;
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 120px;
    justify-content: center;
}

.power-up-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 0 #2fa3d7, 0 0 15px rgba(79, 195, 247, 0.4);
}

.power-up-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 2px 0 #2fa3d7;
}

.power-up-icon {
    font-size: 1.2rem;
}

.power-up-text {
    font-size: 0.9rem;
}

/* Score Section */
.score-section {
    background: linear-gradient(145deg, #1a1a3a, #0f0f2a);
    border: 2px solid rgba(79, 195, 247, 0.3);
    border-radius: 15px;
    padding: 15px;
    margin-top: 10px;
    box-shadow: 0 0 20px rgba(0, 150, 255, 0.2);
}

.score-display {
    display: flex;
    justify-content: space-around;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 10px;
}

.score-item {
    text-align: center;
    min-width: 80px;
}

.score-label {
    display: block;
    color: #4fc3f7;
    font-size: 0.9rem;
    font-weight: bold;
    margin-bottom: 5px;
    text-shadow: 0 0 10px rgba(79, 195, 247, 0.3);
}

.score-value {
    display: block;
    color: #fff;
    font-size: 1.3rem;
    font-weight: bold;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(79, 195, 247, 0.2);
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid rgba(79, 195, 247, 0.3);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4fc3f7, #00bcd4);
    width: 0%;
    transition: width 0.3s ease;
    box-shadow: 0 0 10px rgba(79, 195, 247, 0.5);
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .game-container {
        max-width: 95vw;
    }
}

@media (max-width: 1024px) {
    .game-container {
        max-width: 98vw;
    }
    
    .quiz-section {
        padding: 12px;
        gap: 12px;
    }
    
    .header {
        padding: 10px 15px;
        margin-bottom: 12px;
    }
    
    .question {
        font-size: 1.1rem;
        padding: 12px;
        margin-bottom: 12px;
    }
    
    .power-ups {
        gap: 12px;
        margin: 12px 0;
    }
    
    .power-up-btn {
        padding: 8px 16px;
        font-size: 0.9rem;
        min-width: 100px;
    }
    
    .score-section {
        padding: 12px;
        margin-top: 8px;
    }
    
    .score-value {
        font-size: 1.1rem;
    }
}

@media (max-width: 768px) {
    body {
        padding: 5px;
    }
    
    .game-container {
        max-width: 100vw;
    }
    
    .quiz-section {
        padding: 10px;
        gap: 10px;
    }
    
    .header {
        padding: 8px 12px;
        margin-bottom: 10px;
        flex-direction: column;
        gap: 8px;
    }
    
    .header .timer, .header .lives {
        font-size: 1rem;
    }
    
    .question {
        font-size: 1rem;
        padding: 10px;
        margin-bottom: 10px;
    }
    
    .power-ups {
        gap: 8px;
        margin: 10px 0;
    }
    
    .power-up-btn {
        padding: 6px 12px;
        font-size: 0.8rem;
        min-width: 80px;
    }
    
    .score-section {
        padding: 10px;
        margin-top: 6px;
    }
    
    .score-display {
        flex-direction: column;
        gap: 8px;
    }
    
    .score-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        min-width: auto;
    }
    
    .score-label {
        margin-bottom: 0;
    }
    
    .score-value {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    body {
        padding: 2px;
    }
    
    .game-container {
        max-width: 100vw;
    }
    
    .quiz-section {
        padding: 8px;
        gap: 8px;
    }
    
    .header {
        padding: 6px 10px;
        margin-bottom: 8px;
        gap: 6px;
    }
    
    .header .timer, .header .lives {
        font-size: 0.9rem;
    }
    
    .question {
        font-size: 0.9rem;
        padding: 8px;
        margin-bottom: 8px;
    }
    
    .power-ups {
        gap: 6px;
        margin: 8px 0;
    }
    
    .power-up-btn {
        padding: 5px 10px;
        font-size: 0.75rem;
        min-width: 70px;
    }
    
    .power-up-icon {
        font-size: 1rem;
    }
    
    .power-up-text {
        font-size: 0.8rem;
    }
    
    .score-section {
        padding: 8px;
        margin-top: 5px;
    }
    
    .score-label {
        font-size: 0.8rem;
    }
    
    .score-value {
        font-size: 0.9rem;
    }
}

@media (max-width: 360px) {
    .game-container {
        max-width: 100vw;
    }
    
    .quiz-section {
        padding: 5px;
        gap: 6px;
    }
    
    .header {
        padding: 5px 8px;
        margin-bottom: 6px;
    }
    
    .question {
        font-size: 0.85rem;
        padding: 6px;
        margin-bottom: 6px;
    }
    
    .power-ups {
        gap: 4px;
        margin: 6px 0;
    }
    
    .power-up-btn {
        padding: 4px 8px;
        font-size: 0.7rem;
        min-width: 60px;
    }
    
    .score-section {
        padding: 6px;
        margin-top: 4px;
    }
}

/* Landscape orientation adjustments */
@media (max-height: 600px) and (orientation: landscape) {
    body {
        padding: 5px;
    }
    
    .game-container {
        min-height: auto;
    }
    
    .game-area {
        min-height: auto;
    }
    
    .quiz-section {
        padding: 8px;
        gap: 8px;
    }
    
    .header {
        padding: 6px 12px;
        margin-bottom: 8px;
    }
    
    .question {
        font-size: 1rem;
        padding: 8px;
        margin-bottom: 8px;
    }
    
    .power-ups {
        gap: 8px;
        margin: 8px 0;
    }
    
    .power-up-btn {
        padding: 6px 12px;
        font-size: 0.8rem;
        min-width: 80px;
    }
    
    .score-section {
        padding: 8px;
        margin-top: 6px;
    }
}

/* Volume Control */
.volume-control {
    display: flex;
    align-items: center;
    margin-left: 15px;
    padding: 5px 10px;
    background-color: rgba(42, 42, 74, 0.5);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.volume-control:hover {
    background-color: rgba(42, 42, 74, 0.8);
}

.volume-control label {
    margin-right: 8px;
    font-size: 1rem;
    color: #ccc;
    cursor: pointer;
}

.volume-control input[type="range"] {
    width: 80px;
    height: 4px;
    -webkit-appearance: none;
    appearance: none;
    background: #2a2a4a;
    border-radius: 2px;
    outline: none;
}

.volume-control input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #4fc3f7;
    cursor: pointer;
    transition: all 0.2s;
}

.volume-control input[type="range"]::-webkit-slider-thumb:hover {
    background: #3fb3e7;
    transform: scale(1.1);
}

.volume-control input[type="range"]::-moz-range-thumb {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #4fc3f7;
    cursor: pointer;
    border: none;
    transition: all 0.2s;
}

.volume-control input[type="range"]::-moz-range-thumb:hover {
    background: #3fb3e7;
    transform: scale(1.1);
}

.volume-control #volume-value {
    margin-left: 8px;
    min-width: 35px;
    font-size: 0.8rem;
    color: #ccc;
}

/* Responsive adjustments for volume control */
@media (max-width: 768px) {
    .volume-control {
        margin-top: 10px;
        margin-left: 0;
        width: 100%;
        justify-content: space-between;
    }
    
    .volume-control input[type="range"] {
        width: 60%;
    }
}

/* Instruction Modal Styles */
#instruction-title {
    color: #4fc3f7;
    font-size: 2.5rem;
    margin-bottom: 30px;
    text-shadow: 0 0 10px rgba(79, 195, 247, 0.5);
}

.instructions {
    background: rgba(79, 195, 247, 0.1);
    padding: 25px;
    border-radius: 15px;
    border: 1px solid rgba(79, 195, 247, 0.2);
    margin-bottom: 30px;
    text-align: left;
}

.instructions p {
    color: #fff;
    font-size: 1.2rem;
    margin-bottom: 20px;
    line-height: 1.6;
}

.instructions ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.instructions li {
    color: #fff;
    font-size: 1.1rem;
    margin-bottom: 15px;
    padding-left: 30px;
    position: relative;
    line-height: 1.5;
}

.instructions li::before {
    content: '⚡';
    position: absolute;
    left: 0;
    color: #ffeb3b;
    font-size: 1.2rem;
    animation: sparkle 1.5s infinite;
}

#start-btn {
    background: linear-gradient(145deg, #4fc3f7, #3fb3e7);
    color: #0b0b23;
    padding: 15px 40px;
    border: none;
    border-radius: 12px;
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 200px;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 0 #2fa3d7;
    margin-top: 20px;
}

#start-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 0 #2fa3d7;
}

#start-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(79, 195, 247, 0.3);
}

/* Responsive styles for instruction modal */
@media (max-width: 480px) {
    #instruction-title {
        font-size: 2rem;
    }

    .instructions {
        padding: 20px;
    }

    .instructions p {
        font-size: 1.1rem;
    }

    .instructions li {
        font-size: 1rem;
        padding-left: 25px;
    }

    #start-btn {
        padding: 12px 30px;
        font-size: 1.1rem;
        min-width: 160px;
    }
}

/* Unified modal button styles for result modal (matching game3) */
.replay-btn, .main-menu-btn {
    padding: 15px 30px;
    border: none;
    border-radius: 12px;
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 150px;
    text-transform: uppercase;
    letter-spacing: 1px;
}
.replay-btn {
    background: linear-gradient(145deg, #4fc3f7, #3fb3e7);
    color: #0b0b23;
    box-shadow: 0 4px 0 #2fa3d7;
}
.replay-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 0 #2fa3d7;
}
.main-menu-btn {
    background: linear-gradient(145deg, #f44336, #d32f2f);
    color: #fff;
    box-shadow: 0 4px 0 #b71c1c;
}
.main-menu-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 0 #b71c1c;
}