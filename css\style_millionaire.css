/* Millionaire Quiz Game Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #0b0b23;
    color: #fff;
    height: 100vh;
    min-height: 100vh;
    overflow: hidden;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-container {
    height: auto;
    min-width: 80vw;
    max-width: 100vw;
    display: flex;
    flex-direction: column;
    justify-content: stretch;
    align-items: stretch;
    padding: 0;
    margin: 0;
    overflow: auto;
    position: relative;
    background-color: #1a1a3a;
    border-radius: 15px;
    box-shadow: 0 0 30px rgba(0, 150, 255, 0.2);
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
}

.modal.show {
    display: flex; /* Show modal when .show is present */
}

.modal-content {
    background: linear-gradient(145deg, #1a1a3a, #0f0f2a);
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 0 30px rgba(0, 150, 255, 0.3);
    border: 1px solid rgba(79, 195, 247, 0.2);
    animation: modalAppear 0.5s ease-out;
}

@keyframes modalAppear {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.modal h2 {
    color: #4fc3f7;
    margin-bottom: 25px;
    font-size: 2.5rem;
    text-shadow: 0 0 10px rgba(79, 195, 247, 0.5);
    font-weight: normal;
}

.instructions {
    background: rgba(79, 195, 247, 0.1);
    padding: 25px;
    border-radius: 12px;
    border: 1px solid rgba(79, 195, 247, 0.2);
    margin-bottom: 30px;
    text-align: left;
}

.instructions p {
    color: #fff;
    font-size: 1.2rem;
    margin-bottom: 20px;
    line-height: 1.6;
    text-align: center;
}

.instructions ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.instructions li {
    color: #fff;
    font-size: 1.1rem;
    margin-bottom: 15px;
    padding-left: 30px;
    position: relative;
    line-height: 1.5;
}

.instructions li::before {
    content: '⚡';
    position: absolute;
    left: 0;
    color: #ffeb3b;
    font-size: 1.2rem;
}

@keyframes blink {
    0%, 90%, 100% { opacity: 1; }
    95% { opacity: 0.3; }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.7; }
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-8px); }
}

@keyframes goldGlow {
    0%, 100% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.3); }
    50% { box-shadow: 0 0 40px rgba(255, 215, 0, 0.8); }
}

@keyframes celebration {
    0% { transform: scale(1) rotate(0deg); }
    25% { transform: scale(1.1) rotate(-5deg); }
    50% { transform: scale(1.2) rotate(0deg); }
    75% { transform: scale(1.1) rotate(5deg); }
    100% { transform: scale(1) rotate(0deg); }
}

@keyframes confetti {
    0% { transform: translateY(-100vh) rotate(0deg); opacity: 1; }
    100% { transform: translateY(100vh) rotate(720deg); opacity: 0; }
}

/* Modal Buttons */
.modal-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 30px;
}

.start-btn, .replay-btn, .continue-btn, .try-again-btn, .exit-btn, .main-menu-btn {
    padding: 15px 30px;
    border: none;
    border-radius: 12px;
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 180px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.start-btn, .replay-btn, .continue-btn {
    background: linear-gradient(145deg, #4caf50, #388e3c);
    color: white;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.main-menu-btn, .exit-btn {
    background: linear-gradient(145deg, #f44336, #d32f2f);
    color: white;
    box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
}

.start-btn:hover, .replay-btn:hover, .continue-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.main-menu-btn:hover, .exit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
}

.start-btn:active, .replay-btn:active, .continue-btn:active,
.main-menu-btn:active, .exit-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

/* Buttons */
button {
    background: linear-gradient(145deg, #ffd700, #ffed4e);
    color: #1a1a2e;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
}

button:disabled {
    background: #666;
    color: #999;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Game Area */
.game-area {
    display: flex;
    flex-direction: column;
    flex: 1 1 auto;
    height: 100%;
    overflow-y: auto;
    padding: 10px 8px 8px 8px;
    gap: 8px;
    max-height: 100vh;
}

.game-header {
    margin-bottom: 6px;
    padding: 6px 8px;
    font-size: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(79, 195, 247, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(79, 195, 247, 0.2);
    border-radius: 15px;
    flex-shrink: 0;
}

.robot-host-game .robot-head {
    width: 60px;
    height: 60px;
}

.game-info {
    display: flex;
    gap: 30px;
    align-items: center;
}

.timer, .current-prize {
    background: rgba(79, 195, 247, 0.2);
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 18px;
    font-weight: bold;
    border: 2px solid #4fc3f7;
    color: #4fc3f7;
}

.lives {
    margin-left: 12px;
    font-size: 1.2em;
    color: #ffd700;
    display: flex;
    align-items: center;
    gap: 4px;
    font-weight: bold;
}

/* Question Area */
.question-area {
    padding: 10px 6px;
    margin-bottom: 6px;
    min-height: 0;
    flex: 1 1 0;
    gap: 6px;
    background: rgba(79, 195, 247, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(79, 195, 247, 0.2);
    border-radius: 15px;
    display: flex;
    flex-direction: column;
    overflow: visible;
    position: relative;
}

/* Horizontal Prize Ladder */
.prize-ladder-horizontal {
    margin-bottom: 6px;
    padding: 6px 8px;
    background: rgba(79, 195, 247, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(79, 195, 247, 0.2);
    border-radius: 15px;
    flex-shrink: 0;
}

.prize-ladder-horizontal h3 {
    color: #ffd700;
    text-align: center;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    font-size: 18px;
}

.ladder-scroll-container {
    padding: 4px 0;
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: thin;
    scrollbar-color: #ffd700 rgba(255, 255, 255, 0.1);
}

.ladder-scroll-container::-webkit-scrollbar {
    height: 8px;
}

.ladder-scroll-container::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.ladder-scroll-container::-webkit-scrollbar-thumb {
    background: #ffd700;
    border-radius: 4px;
}

.ladder-horizontal {
    gap: 6px;
    display: flex;
    min-width: max-content;
    padding: 5px;
}

.prize-item-horizontal {
    padding: 6px 10px;
    min-width: 70px;
    font-size: 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 25px;
    font-weight: bold;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    white-space: nowrap;
    text-align: center;
    cursor: pointer;
}

.prize-item-horizontal.current {
    background: linear-gradient(145deg, #ffd700, #ffed4e);
    color: #1a1a2e;
    transform: scale(1.1);
    border: 2px solid #fff;
    animation: goldGlow 2s infinite;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
}

.prize-item-horizontal.safe-point {
    border: 2px solid #00ff00;
    background: rgba(0, 255, 0, 0.2);
}

.prize-item-horizontal.completed {
    background: rgba(0, 255, 0, 0.3);
    border: 2px solid #00ff00;
    color: #fff;
}

.prize-item-horizontal:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.question-header {
    margin-bottom: 6px;
    font-size: 1rem;
    display: flex;
    justify-content: space-between;
    color: #ffd700;
}

/* Host and Question Container */
.host-question-container {
    gap: 10px;
    margin-bottom: 6px;
    min-height: 80px;
    display: flex;
    align-items: flex-start;
    overflow: visible;
    padding-top: 50px;
    position: relative;
}

.question-text {
    font-size: 20px;
    line-height: 1.4;
    color: #fff;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
    flex: 1;
    padding: 20px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.06));
    border-radius: 20px;
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-left: 6px solid #ffd700;
    margin: 0;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
}

.question-text::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #ffd700, #ffed4e, #ffd700);
    border-radius: 20px;
    z-index: -1;
    opacity: 0.3;
    animation: questionGlow 3s ease-in-out infinite;
}

/* Pikachu Host Styles */
.quiz-host {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    min-width: 100px;
    z-index: 1000;
    margin-top: -30px;
    flex-shrink: 0;
}

.pikachu-character {
    position: relative;
    animation: hostBounce 4s ease-in-out infinite;
    filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.4));
    z-index: 500;
}

.pikachu-character::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: radial-gradient(circle, rgba(255, 235, 59, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    z-index: -1;
    animation: pikachuGlow 3s ease-in-out infinite;
}

.pikachu-body {
    width: 70px;
    height: 70px;
    background: radial-gradient(circle at 30% 30%, #ffeb3b, #ffc107, #ff8f00);
    border-radius: 50%;
    position: relative;
    border: 3px solid #ff6f00;
    box-shadow:
        inset 0 -8px 15px rgba(255, 193, 7, 0.8),
        inset 0 8px 15px rgba(255, 235, 59, 0.8),
        0 6px 12px rgba(0, 0, 0, 0.3);
}

.pikachu-face {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50px;
    height: 42px;
}

.pikachu-eyes {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    padding: 0 8px;
}

.eye {
    width: 12px;
    height: 12px;
    background: radial-gradient(circle at 30% 30%, #333, #000);
    border-radius: 50%;
    animation: blink 5s infinite;
    position: relative;
}

.eye::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 3px;
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
}

.pikachu-cheeks {
    position: absolute;
    top: 18px;
    width: 100%;
}

.cheek {
    width: 16px;
    height: 16px;
    background: radial-gradient(circle at 30% 30%, #ff5722, #d32f2f);
    border-radius: 50%;
    position: absolute;
    animation: cheekGlow 3s ease-in-out infinite;
    box-shadow: 0 0 10px rgba(255, 87, 34, 0.6);
}

.left-cheek {
    left: -8px;
}

.right-cheek {
    right: -8px;
}

.pikachu-mouth {
    width: 8px;
    height: 6px;
    background: #000;
    border-radius: 0 0 50% 50%;
    margin: 12px auto 0;
    position: relative;
}

.pikachu-mouth::before {
    content: '';
    position: absolute;
    top: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 4px;
    background: #000;
}

.pikachu-ears {
    position: absolute;
    top: -20px;
    width: 100%;
    z-index: -1;
}

.ear {
    width: 20px;
    height: 28px;
    background: radial-gradient(ellipse at 30% 30%, #ffeb3b, #ffc107, #ff8f00);
    border: 2px solid #ff6f00;
    position: absolute;
    border-radius: 60% 60% 20% 20%;
    box-shadow:
        inset 0 -4px 8px rgba(255, 193, 7, 0.8),
        0 3px 6px rgba(0, 0, 0, 0.3);
    z-index: -1;
}

.left-ear {
    left: 8px;
    transform: rotate(-25deg);
    animation: earWiggle 4s ease-in-out infinite;
}

.right-ear {
    right: 8px;
    transform: rotate(25deg);
    animation: earWiggle 4s ease-in-out infinite 0.5s;
}

.ear::after {
    content: '';
    position: absolute;
    top: 8px;
    left: 50%;
    transform: translateX(-50%);
    width: 12px;
    height: 18px;
    background: radial-gradient(ellipse at 50% 30%, #333, #000);
    border-radius: 60% 60% 20% 20%;
}

.pikachu-arms {
    position: absolute;
    top: 52px;
    width: 100%;
}

.arm {
    width: 14px;
    height: 25px;
    background: radial-gradient(ellipse at 30% 30%, #ffeb3b, #ffc107, #ff8f00);
    border: 2px solid #ff6f00;
    border-radius: 60%;
    position: absolute;
    box-shadow:
        inset 0 -4px 8px rgba(255, 193, 7, 0.8),
        0 3px 6px rgba(0, 0, 0, 0.3);
}

.left-arm {
    left: -8px;
    transform: rotate(-35deg);
    animation: armWave 3s ease-in-out infinite;
}

.right-arm {
    right: -8px;
    transform: rotate(35deg);
}

.right-arm.pointing {
    animation: pointToQuestion 2.5s ease-in-out infinite;
    transform-origin: bottom center;
}

/* Pikachu tail */
.pikachu-character::after {
    content: '';
    position: absolute;
    bottom: -10px;
    right: -18px;
    width: 20px;
    height: 28px;
    background: radial-gradient(ellipse at 30% 30%, #ffeb3b, #ffc107);
    border: 2px solid #ff6f00;
    border-radius: 60% 20% 80% 40%;
    transform: rotate(45deg);
    animation: tailWag 3s ease-in-out infinite;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
}

/* Speech Bubble */
.host-speech-bubble {
    position: absolute;
    top: -50px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    color: #2c3e50;
    padding: 10px 15px;
    border-radius: 18px;
    font-size: 12px;
    font-weight: 600;
    white-space: nowrap;
    box-shadow:
        0 6px 20px rgba(0, 0, 0, 0.15),
        0 2px 8px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    border: 2px solid #ffd700;
    opacity: 0;
    animation: speechBubble 5s ease-in-out infinite;
    min-width: 100px;
    max-width: 200px;
    text-align: center;
    letter-spacing: 0.3px;
    z-index: 9999;
    word-wrap: break-word;
    white-space: normal;
}

.host-speech-bubble::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 10px solid transparent;
    border-top-color: #ffd700;
    z-index: 9999;
}

.host-speech-bubble::before {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(-2px);
    border: 8px solid transparent;
    border-top-color: #ffffff;
    z-index: 10000;
}

.speech-bubble-content {
    position: relative;
    z-index: 10001;
}

.options-container {
    gap: 6px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    margin-top: 15px;
}

.option {
    font-size: 1rem;
    padding: 8px 6px;
    background: rgba(79, 195, 247, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid rgba(79, 195, 247, 0.2);
    display: flex;
    align-items: center;
    color: #fff;
}

.option:hover {
    background: rgba(79, 195, 247, 0.2);
    border-color: #4fc3f7;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(79, 195, 247, 0.3);
}

.option.selected {
    background: linear-gradient(145deg, #4fc3f7, #3fb3e7);
    color: #0b0b23;
    border-color: #fff;
    box-shadow: 0 4px 15px rgba(79, 195, 247, 0.4);
}

.option.correct {
    background: linear-gradient(145deg, #4caf50, #388e3c);
    color: #fff;
    border-color: #fff;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);
}

.option.incorrect {
    background: linear-gradient(145deg, #f44336, #d32f2f);
    color: #fff;
    border-color: #fff;
    box-shadow: 0 4px 15px rgba(244, 67, 54, 0.4);
}

.option.eliminated {
    opacity: 0.3;
    pointer-events: none;
    background: #2a2a4a;
    border-color: #555;
}

.option-letter {
    font-weight: bold;
    margin-right: 10px;
    font-size: 18px;
}

.option-text {
    flex: 1;
    font-size: 16px;
}

/* Game Controls */
.game-controls {
    margin-top: 0;
    gap: 6px;
    padding: 6px 0 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(79, 195, 247, 0.1);
    padding: 15px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(79, 195, 247, 0.2);
    flex-shrink: 0;
    margin-top: auto;
}

.lifelines {
    display: flex;
    gap: 15px;
}

.lifeline {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px 15px;
    background: linear-gradient(145deg, #4fc3f7, #3fb3e7);
    color: #0b0b23;
    min-width: 80px;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
    box-shadow: 0 4px 0 #2fa3d7;
}

.lifeline:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 0 #2fa3d7;
}

.lifeline:disabled {
    background: #666;
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.lifeline-icon {
    font-size: 20px;
    margin-bottom: 5px;
}

.lifeline-text {
    font-size: 12px;
}

.action-buttons {
    display: flex;
    gap: 15px;
}

.walk-away {
    background: linear-gradient(145deg, #f44336, #d32f2f);
    color: #fff;
    padding: 15px 30px;
    border: none;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 0 #b71c1c;
}

.walk-away:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 0 #b71c1c;
}

.final-answer {
    background: linear-gradient(145deg, #4caf50, #388e3c);
    color: #fff;
    font-size: 1.1rem;
    padding: 15px 30px;
    border: none;
    border-radius: 12px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 0 #2e7d32;
}

.final-answer:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 0 #2e7d32;
}

.final-answer:disabled {
    background: #666;
    color: #999;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Audience Results */
.audience-results {
    margin: 20px 0;
}

.audience-bar {
    display: flex;
    align-items: center;
    margin: 10px 0;
    gap: 10px;
}

.option-label {
    font-weight: bold;
    width: 30px;
    color: #ffd700;
}

.bar-container {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 10px;
}

.bar {
    height: 25px;
    background: linear-gradient(145deg, #4a90e2, #357abd);
    border-radius: 12px;
    transition: width 1s ease;
    min-width: 2px;
}

.percentage {
    font-weight: bold;
    color: #ffd700;
    min-width: 40px;
}

/* Friend Advice */
.friend-advice {
    margin: 20px 0;
    padding: 20px;
    background: rgba(79, 195, 247, 0.1);
    border-radius: 10px;
    border: 1px solid rgba(79, 195, 247, 0.2);
    font-style: italic;
}

/* Modal Close Buttons */
#close-audience-modal, #close-friend-modal {
    background: linear-gradient(145deg, #4fc3f7, #3fb3e7);
    color: #0b0b23;
    padding: 12px 25px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 20px;
    box-shadow: 0 4px 0 #2fa3d7;
}

#close-audience-modal:hover, #close-friend-modal:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 0 #2fa3d7;
}

/* Result Content */
.result-content {
    margin: 20px 0;
}

.final-stats {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 10px;
    margin-top: 15px;
}

.final-stats p {
    margin: 5px 0;
    color: #e0e0e0;
}

.result-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
}

/* Stars */
.stars {
    font-size: 2.5rem;
    margin: 20px 0;
    letter-spacing: 10px;
}

.star {
    color: #ffd700;
}

.star.empty {
    color: #333;
}

/* Result Message */
#result-message {
    color: #fff;
    font-size: 1.2rem;
    margin: 20px 0;
    line-height: 1.5;
}

/* EXP Container */
.exp-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin: 20px 0;
    padding: 15px;
    background: rgba(79, 195, 247, 0.1);
    border-radius: 12px;
    border: 1px solid rgba(79, 195, 247, 0.2);
    animation: expPulse 2s infinite;
}

@keyframes expPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 15px rgba(79, 195, 247, 0.2);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 25px rgba(79, 195, 247, 0.4);
    }
}

.exp-icon {
    font-size: 1.8rem;
    animation: sparkle 1.5s infinite;
}

@keyframes sparkle {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
    50% {
        transform: scale(1.2) rotate(15deg);
        opacity: 0.8;
    }
}

.exp-text {
    color: #ffeb3b;
    font-size: 1.5rem;
    font-weight: bold;
    text-shadow: 0 0 10px rgba(255, 235, 59, 0.5);
}

/* Modal Buttons */
.modal-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 30px;
}

.start-btn, .replay-btn, .continue-btn {
    background: linear-gradient(145deg, #4fc3f7, #3fb3e7);
    color: #0b0b23;
    box-shadow: 0 4px 0 #2fa3d7;
}

.start-btn:hover, .replay-btn:hover, .continue-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 0 #2fa3d7;
}

.main-menu-btn {
    background: linear-gradient(145deg, #f44336, #d32f2f);
    color: #fff;
    box-shadow: 0 4px 0 #b71c1c;
}

.main-menu-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 0 #b71c1c;
}

.replay-btn, .main-menu-btn, .start-btn {
    padding: 15px 30px;
    border: none;
    border-radius: 12px;
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 150px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

#start-btn {
    background: linear-gradient(145deg, #4fc3f7, #3fb3e7);
    color: #0b0b23;
    padding: 15px 40px;
    border: none;
    border-radius: 12px;
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 200px;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 0 #2fa3d7;
    margin-top: 20px;
}

#start-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 0 #2fa3d7;
}

#start-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(79, 195, 247, 0.3);
}

/* Celebration Effects */
.millionaire-celebration {
    animation: celebration 2s infinite;
}

.confetti {
    position: fixed;
    width: 10px;
    height: 10px;
    background: #ffd700;
    animation: confetti 3s linear infinite;
    z-index: 1001;
}

.confetti:nth-child(odd) {
    background: #ff6b6b;
    animation-delay: -0.5s;
}

.confetti:nth-child(even) {
    background: #4a90e2;
    animation-delay: -1s;
}

/* Animations */
@keyframes correctAnswer {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes incorrectAnswer {
    0% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
    100% { transform: translateX(0); }
}

.option.correct {
    animation: correctAnswer 0.6s ease;
}

.option.incorrect {
    animation: incorrectAnswer 0.6s ease;
}

/* Pikachu Host Animations */
@keyframes hostBounce {
    0%, 100% {
        transform: translateY(0px) scale(1);
    }
    25% {
        transform: translateY(-3px) scale(1.02);
    }
    50% {
        transform: translateY(-8px) scale(1.05);
    }
    75% {
        transform: translateY(-3px) scale(1.02);
    }
}

@keyframes pointToQuestion {
    0%, 100% {
        transform: rotate(35deg) translateY(0px) scale(1);
    }
    50% {
        transform: rotate(50deg) translateY(-5px) scale(1.1);
    }
}

@keyframes blink {
    0%, 85%, 100% {
        transform: scaleY(1);
        opacity: 1;
    }
    90%, 95% {
        transform: scaleY(0.1);
        opacity: 0.8;
    }
}

@keyframes speechBubble {
    0%, 75%, 100% {
        opacity: 0;
        transform: translateX(-50%) translateY(10px) scale(0.8);
    }
    15%, 65% {
        opacity: 1;
        transform: translateX(-50%) translateY(0px) scale(1);
    }
}

@keyframes celebration {
    0%, 100% {
        transform: scale(1) rotate(0deg) translateY(0px);
    }
    25% {
        transform: scale(1.15) rotate(-5deg) translateY(-5px);
    }
    50% {
        transform: scale(1.2) rotate(0deg) translateY(-10px);
    }
    75% {
        transform: scale(1.15) rotate(5deg) translateY(-5px);
    }
}

@keyframes questionGlow {
    0%, 100% {
        opacity: 0.2;
    }
    50% {
        opacity: 0.4;
    }
}

@keyframes cheekGlow {
    0%, 100% {
        box-shadow: 0 0 10px rgba(255, 87, 34, 0.6);
    }
    50% {
        box-shadow: 0 0 20px rgba(255, 87, 34, 0.9);
    }
}

@keyframes earWiggle {
    0%, 100% {
        transform: rotate(-25deg);
    }
    50% {
        transform: rotate(-20deg);
    }
}

@keyframes armWave {
    0%, 100% {
        transform: rotate(-35deg);
    }
    50% {
        transform: rotate(-25deg);
    }
}

@keyframes tailWag {
    0%, 100% {
        transform: rotate(45deg);
    }
    50% {
        transform: rotate(55deg);
    }
}

@keyframes pikachuGlow {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 0.6;
        transform: scale(1.1);
    }
}

@keyframes modalGlow {
    0%, 100% {
        opacity: 0.8;
    }
    50% {
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .question-area {
        padding: 18px;
    }

    .question-text {
        font-size: 18px;
        padding: 18px;
    }

    .game-area {
        max-height: 100vh;
        overflow-y: auto;
    }
}

@media (max-width: 768px) {
    .question-area {
        padding: 15px;
    }

    .options-container {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .option {
        padding: 10px;
    }

    .game-controls {
        flex-direction: column;
        gap: 12px;
        padding: 12px;
    }

    .lifelines {
        order: 2;
        justify-content: center;
    }

    .action-buttons {
        order: 1;
        justify-content: center;
    }

    .host-question-container {
        flex-direction: column;
        align-items: center;
        gap: 15px;
        min-height: auto;
        padding-top: 60px;
        overflow: visible;
    }

    .quiz-host {
        min-width: 90px;
        order: -1;
        margin-top: -40px;
    }

    .question-text {
        font-size: 16px;
        padding: 15px;
        line-height: 1.3;
    }

    .pikachu-body {
        width: 60px;
        height: 60px;
    }

    .pikachu-face {
        width: 42px;
        height: 36px;
    }

    .ear {
        width: 16px;
        height: 22px;
    }

    .arm {
        width: 12px;
        height: 20px;
    }

    .host-speech-bubble {
        font-size: 11px;
        padding: 6px 12px;
        min-width: 80px;
        max-width: 150px;
        top: -40px;
    }

    .pikachu-character::after {
        width: 16px;
        height: 22px;
        right: -14px;
        bottom: -8px;
    }

    .modal-content {
        max-width: 95vw;
        width: 98vw;
        padding: 20px;
        border-radius: 12px;
    }

    .stars {
        font-size: 2rem;
        letter-spacing: 8px;
        margin: 15px 0;
    }

    .exp-container {
        padding: 12px;
        margin: 15px 0;
    }

    .exp-icon {
        font-size: 1.5rem;
    }

    .exp-text {
        font-size: 1.2rem;
    }

    .modal-buttons {
        flex-direction: column;
        gap: 15px;
        margin-top: 20px;
    }

    .game-area {
        max-height: 100vh;
        overflow-y: auto;
    }
}

@media (max-width: 480px) {
    .prize-item-horizontal {
        min-width: 60px;
        padding: 6px 10px;
        font-size: 10px;
    }

    .ladder-horizontal {
        gap: 5px;
    }

    .prize-ladder-horizontal {
        padding: 8px;
    }

    .game-header {
        flex-direction: column;
        gap: 8px;
    }

    .game-info {
        gap: 12px;
    }

    .host-question-container {
        gap: 10px;
        min-height: 100px;
        padding-top: 50px;
        overflow: visible;
    }

    .quiz-host {
        min-width: 70px;
        margin-top: -30px;
    }

    .pikachu-body {
        width: 50px;
        height: 50px;
    }

    .pikachu-face {
        width: 35px;
        height: 30px;
    }

    .ear {
        width: 14px;
        height: 18px;
    }

    .arm {
        width: 10px;
        height: 16px;
    }

    .host-speech-bubble {
        font-size: 9px;
        padding: 4px 8px;
        min-width: 60px;
        max-width: 120px;
        top: -35px;
    }

    .question-text {
        font-size: 14px;
        padding: 12px;
        line-height: 1.2;
    }

    .pikachu-character::after {
        width: 12px;
        height: 16px;
        right: -10px;
        bottom: -6px;
    }

    .modal-content {
        width: 300px;
        height: 300px;
        padding: 20px;
    }

    .modal h2, .modal h3 {
        font-size: 20px;
        margin-bottom: 15px;
    }

    .stars {
        font-size: 1.5rem;
        letter-spacing: 5px;
        margin: 10px 0;
    }

    .exp-container {
        padding: 8px;
        margin: 10px 0;
    }

    .exp-icon {
        font-size: 1.2rem;
    }

    .exp-text {
        font-size: 1rem;
    }

    .option {
        padding: 8px;
        font-size: 14px;
    }

    .game-controls {
        padding: 10px;
        gap: 10px;
    }

    .game-area {
        max-height: 100vh;
        overflow-y: auto;
    }
}
