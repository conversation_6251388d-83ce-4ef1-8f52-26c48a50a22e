/* Matching Quiz Specific Styles */

/* Ensure smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Answer Pool Section */
.answer-pool-section {
    background: linear-gradient(145deg, #1a1a3a, #0f0f2a);
    border: 2px solid rgba(79, 195, 247, 0.3);
    border-radius: 15px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 0 20px rgba(0, 150, 255, 0.2);
    overflow: hidden;
}

.pool-title {
    color: #4fc3f7;
    font-size: 1.2rem;
    margin-bottom: 10px;
    text-align: center;
    text-shadow: 0 0 10px rgba(79, 195, 247, 0.5);
}

.answer-pool {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    min-height: 60px;
    padding: 8px;
    border: 2px dashed rgba(79, 195, 247, 0.3);
    border-radius: 10px;
    background: rgba(79, 195, 247, 0.05);
    align-items: center;
}

/* Answer Options in Pool */
.answer-option {
    background: linear-gradient(145deg, #4fc3f7, #3fb3e7);
    color: #0b0b23;
    padding: 10px 16px;
    border-radius: 20px;
    font-weight: bold;
    cursor: grab;
    user-select: none;
    transition: all 0.3s ease;
    box-shadow: 0 3px 0 #2fa3d7;
    border: none;
    font-size: 0.9rem;
    min-width: 100px;
    text-align: center;
    position: relative;
    z-index: 10;
    word-wrap: break-word;
    hyphens: auto;
}

.answer-option:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 0 #2fa3d7, 0 0 15px rgba(79, 195, 247, 0.4);
}

.answer-option:active,
.answer-option.dragging {
    cursor: grabbing;
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(79, 195, 247, 0.6);
    z-index: 1000;
}

.answer-option.used {
    opacity: 0.5;
    cursor: not-allowed;
    background: linear-gradient(145deg, #666, #555);
    box-shadow: 0 2px 0 #444;
}

.answer-option.used:hover {
    transform: none;
    box-shadow: 0 2px 0 #444;
}

/* Question Section */
.question-section {
    background: linear-gradient(145deg, #1a1a3a, #0f0f2a);
    border: 2px solid rgba(79, 195, 247, 0.3);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 0 20px rgba(0, 150, 255, 0.2);
    overflow: hidden;
}

.question {
    font-size: 1.1rem;
    color: #fff;
    margin-bottom: 15px;
    line-height: 1.4;
    text-align: center;
    word-wrap: break-word;
    hyphens: auto;
}

/* Drop Zones */
.drop-zones {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 15px;
    align-items: center;
    width: 100%;
}

.drop-zone {
    min-height: 60px;
    min-width: 250px;
    max-width: 400px;
    width: 100%;
    border: 3px dashed rgba(79, 195, 247, 0.4);
    border-radius: 12px;
    background: rgba(79, 195, 247, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 15px;
    transition: all 0.3s ease;
    position: relative;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.6);
    text-align: center;
    word-wrap: break-word;
    hyphens: auto;
}

.drop-zone.drag-over {
    border-color: #4fc3f7;
    background: rgba(79, 195, 247, 0.2);
    box-shadow: 0 0 15px rgba(79, 195, 247, 0.4);
    transform: scale(1.02);
}

.drop-zone.filled {
    border-color: #4caf50;
    background: rgba(76, 175, 80, 0.2);
    border-style: solid;
}

.drop-zone.correct {
    border-color: #4caf50;
    background: rgba(76, 175, 80, 0.3);
    animation: correctPulse 0.6s ease-out;
}

.drop-zone.incorrect {
    border-color: #f44336;
    background: rgba(244, 67, 54, 0.3);
    animation: incorrectShake 0.6s ease-out;
}

.drop-zone .placeholder-text {
    font-style: italic;
    opacity: 0.7;
}

.drop-zone .dropped-answer {
    background: linear-gradient(145deg, #4caf50, #45a049);
    color: white;
    padding: 8px 15px;
    border-radius: 18px;
    font-weight: bold;
    box-shadow: 0 3px 0 #3d8b40;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    word-wrap: break-word;
    hyphens: auto;
    max-width: 100%;
}

.drop-zone .dropped-answer:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 0 #3d8b40, 0 0 10px rgba(76, 175, 80, 0.4);
}

/* Navigation Buttons */
.navigation-buttons {
    display: flex;
    justify-content: center;
    margin: 15px 0;
    gap: 20px;
    flex-wrap: wrap;
}

.nav-btn {
    background: linear-gradient(145deg, #4fc3f7, #3fb3e7);
    color: #0b0b23;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 0 #2fa3d7;
    min-width: 120px;
    word-wrap: break-word;
    hyphens: auto;
}

.nav-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 0 #2fa3d7, 0 0 15px rgba(79, 195, 247, 0.4);
}

.nav-btn:active:not(:disabled) {
    transform: translateY(1px);
    box-shadow: 0 2px 0 #2fa3d7;
}

.nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 2px 0 #2fa3d7;
}

/* Question Counter */
.question-counter {
    background: rgba(79, 195, 247, 0.2);
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: bold;
    color: #4fc3f7;
    border: 1px solid rgba(79, 195, 247, 0.3);
}

/* Score Section */
.score-section {
    background: linear-gradient(145deg, #1a1a3a, #0f0f2a);
    border: 2px solid rgba(79, 195, 247, 0.3);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 0 20px rgba(0, 150, 255, 0.2);
}

.score-display {
    display: flex;
    justify-content: space-around;
    margin-bottom: 15px;
}

.score-item {
    text-align: center;
    flex: 1;
}

.score-label {
    display: block;
    color: #4fc3f7;
    font-size: 0.9rem;
    margin-bottom: 5px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.score-value {
    display: block;
    color: #fff;
    font-size: 1.8rem;
    font-weight: bold;
    text-shadow: 0 0 10px rgba(79, 195, 247, 0.5);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(79, 195, 247, 0.2);
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid rgba(79, 195, 247, 0.3);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4fc3f7, #3fb3e7);
    width: 0%;
    transition: width 0.5s ease;
    box-shadow: 0 0 10px rgba(79, 195, 247, 0.6);
}

/* Score animations */
.score-value.updated {
    animation: scoreUpdate 0.6s ease-out;
}

@keyframes scoreUpdate {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); color: #4fc3f7; }
    100% { transform: scale(1); }
}

/* Animations */
@keyframes correctPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); box-shadow: 0 0 20px rgba(76, 175, 80, 0.6); }
    100% { transform: scale(1); }
}

@keyframes incorrectShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

@keyframes dragHint {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-3px); }
}

/* Drag and Drop Visual Feedback */
.drag-hint {
    animation: dragHint 1.5s ease-in-out infinite;
}

.ghost-element {
    opacity: 0.5;
    transform: rotate(5deg);
    pointer-events: none;
}

/* Accessibility - Focus States */
.answer-option:focus,
.drop-zone:focus,
.nav-btn:focus {
    outline: 3px solid #4fc3f7;
    outline-offset: 2px;
}

/* Keyboard Navigation Indicator */
.keyboard-selected {
    outline: 3px solid #ffeb3b;
    outline-offset: 2px;
    animation: keyboardPulse 1s ease-in-out infinite alternate;
}

@keyframes keyboardPulse {
    from { outline-color: #ffeb3b; }
    to { outline-color: #ffc107; }
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .drop-zone {
        max-width: 350px;
    }
}

@media (max-width: 1024px) {
    .answer-pool-section,
    .question-section {
        padding: 15px;
        margin-bottom: 12px;
    }
    
    .pool-title {
        font-size: 1.1rem;
        margin-bottom: 8px;
    }
    
    .answer-pool {
        gap: 8px;
        min-height: 55px;
        padding: 6px;
    }
    
    .answer-option {
        padding: 8px 14px;
        font-size: 0.85rem;
        min-width: 90px;
    }
    
    .question {
        font-size: 1rem;
        margin-bottom: 12px;
    }
    
    .drop-zone {
        min-height: 55px;
        min-width: 220px;
        max-width: 320px;
        padding: 12px;
        font-size: 0.85rem;
    }
    
    .drop-zone .dropped-answer {
        padding: 6px 12px;
        font-size: 0.85rem;
    }
    
    .navigation-buttons {
        margin: 12px 0;
        gap: 15px;
    }
    
    .nav-btn {
        padding: 10px 20px;
        font-size: 0.9rem;
        min-width: 100px;
    }
}

@media (max-width: 768px) {
    .answer-pool {
        gap: 6px;
        min-height: 50px;
        padding: 5px;
    }
    
    .answer-option {
        padding: 6px 10px;
        font-size: 0.8rem;
        min-width: 70px;
    }
    
    .question {
        font-size: 0.95rem;
    }
    
    .drop-zone {
        min-height: 50px;
        min-width: 200px;
        max-width: 280px;
        padding: 10px;
        font-size: 0.8rem;
    }
    
    .drop-zone .dropped-answer {
        padding: 5px 10px;
        font-size: 0.8rem;
    }
    
    .navigation-buttons {
        margin: 10px 0;
        gap: 12px;
    }
    
    .nav-btn {
        padding: 8px 16px;
        font-size: 0.85rem;
        min-width: 80px;
    }
    
    .answer-pool-section,
    .question-section {
        padding: 12px;
        margin-bottom: 10px;
    }
    
    .pool-title {
        font-size: 1rem;
        margin-bottom: 6px;
    }
    
    .question {
        font-size: 0.9rem;
        margin-bottom: 10px;
    }
}

@media (max-width: 480px) {
    .answer-pool-section,
    .question-section {
        padding: 10px;
        margin-bottom: 8px;
    }
    
    .pool-title {
        font-size: 0.9rem;
        margin-bottom: 5px;
    }
    
    .answer-pool {
        gap: 4px;
        min-height: 45px;
        padding: 4px;
    }
    
    .answer-option {
        padding: 5px 8px;
        font-size: 0.75rem;
        min-width: 60px;
    }
    
    .question {
        font-size: 0.85rem;
        margin-bottom: 8px;
    }
    
    .drop-zone {
        min-height: 45px;
        min-width: 180px;
        max-width: 250px;
        padding: 8px;
        font-size: 0.75rem;
    }
    
    .drop-zone .dropped-answer {
        padding: 4px 8px;
        font-size: 0.75rem;
    }
    
    .navigation-buttons {
        margin: 8px 0;
        gap: 10px;
    }
    
    .nav-btn {
        padding: 6px 12px;
        font-size: 0.8rem;
        min-width: 70px;
    }
}

@media (max-width: 360px) {
    .answer-pool-section,
    .question-section {
        padding: 8px;
        margin-bottom: 6px;
    }
    
    .pool-title {
        font-size: 0.85rem;
        margin-bottom: 4px;
    }
    
    .answer-pool {
        gap: 3px;
        min-height: 40px;
        padding: 3px;
    }
    
    .answer-option {
        padding: 4px 6px;
        font-size: 0.7rem;
        min-width: 50px;
    }
    
    .question {
        font-size: 0.8rem;
        margin-bottom: 6px;
    }
    
    .drop-zone {
        min-height: 40px;
        min-width: 160px;
        max-width: 220px;
        padding: 6px;
        font-size: 0.7rem;
    }
    
    .drop-zone .dropped-answer {
        padding: 3px 6px;
        font-size: 0.7rem;
    }
    
    .navigation-buttons {
        margin: 6px 0;
        gap: 8px;
    }
    
    .nav-btn {
        padding: 5px 10px;
        font-size: 0.75rem;
        min-width: 60px;
    }
}

/* Landscape orientation adjustments */
@media (max-height: 600px) and (orientation: landscape) {
    .answer-pool-section,
    .question-section {
        padding: 8px;
        margin-bottom: 6px;
    }
    
    .pool-title {
        font-size: 0.9rem;
        margin-bottom: 4px;
    }
    
    .answer-pool {
        gap: 4px;
        min-height: 40px;
        padding: 4px;
    }
    
    .answer-option {
        padding: 4px 8px;
        font-size: 0.75rem;
        min-width: 60px;
    }
    
    .question {
        font-size: 0.85rem;
        margin-bottom: 6px;
    }
    
    .drop-zone {
        min-height: 40px;
        min-width: 180px;
        max-width: 250px;
        padding: 6px;
        font-size: 0.75rem;
    }
    
    .drop-zone .dropped-answer {
        padding: 3px 6px;
        font-size: 0.75rem;
    }
    
    .navigation-buttons {
        margin: 6px 0;
        gap: 8px;
    }
    
    .nav-btn {
        padding: 5px 10px;
        font-size: 0.8rem;
        min-width: 70px;
    }
}

/* Extra small screens */
@media (max-width: 320px) {
    .answer-pool-section,
    .question-section {
        padding: 6px;
        margin-bottom: 5px;
    }
    
    .pool-title {
        font-size: 0.8rem;
        margin-bottom: 3px;
    }
    
    .answer-pool {
        gap: 2px;
        min-height: 35px;
        padding: 2px;
    }
    
    .answer-option {
        padding: 3px 5px;
        font-size: 0.65rem;
        min-width: 45px;
    }
    
    .question {
        font-size: 0.75rem;
        margin-bottom: 5px;
    }
    
    .drop-zone {
        min-height: 35px;
        min-width: 140px;
        max-width: 200px;
        padding: 5px;
        font-size: 0.65rem;
    }
    
    .drop-zone .dropped-answer {
        padding: 2px 5px;
        font-size: 0.65rem;
    }
    
    .navigation-buttons {
        margin: 5px 0;
        gap: 6px;
    }
    
    .nav-btn {
        padding: 4px 8px;
        font-size: 0.7rem;
        min-width: 50px;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .answer-option {
        border: 2px solid #fff;
    }

    .drop-zone {
        border-width: 4px;
    }

    .answer-option:focus,
    .drop-zone:focus,
    .nav-btn:focus {
        outline: 4px solid #ffff00;
        outline-offset: 3px;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .answer-option,
    .drop-zone,
    .nav-btn {
        transition: none;
    }

    .answer-option:hover,
    .drop-zone.drag-over {
        transform: none;
    }

    @keyframes correctPulse {
        from, to { transform: none; }
    }
    
    @keyframes incorrectShake {
        from, to { transform: none; }
    }
    
    @keyframes dragHint {
        from, to { transform: none; }
    }
    
    @keyframes keyboardPulse {
        from, to { transform: none; }
    }
}

/* Screen Reader Only Content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Drop Zone Labels */
.drop-zone-container {
    margin-bottom: 15px;
    width: 100%;
    max-width: 500px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.drop-zone-label {
    color: #4fc3f7;
    font-weight: bold;
    margin-bottom: 12px;
    font-size: 1.3rem;
    text-shadow: 0 0 5px rgba(79, 195, 247, 0.3);
    text-align: center;
    padding: 10px 20px;
    background: rgba(79, 195, 247, 0.1);
    border-radius: 20px;
    border: 1px solid rgba(79, 195, 247, 0.3);
}

/* Improved Focus Indicators */
.answer-option:focus-visible,
.drop-zone:focus-visible,
.nav-btn:focus-visible,
.power-up-btn:focus-visible {
    outline: 3px solid #4fc3f7;
    outline-offset: 2px;
    box-shadow: 0 0 0 6px rgba(79, 195, 247, 0.3);
}

/* Touch Target Size for Mobile Accessibility */
@media (max-width: 768px) {
    .answer-option,
    .nav-btn,
    .power-up-btn {
        min-height: 44px;
        min-width: 44px;
    }

    .drop-zone {
        min-height: 60px;
    }
}

/* Keyboard Navigation Hints */
.keyboard-hint {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 0.9rem;
    z-index: 1001;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.keyboard-hint.show {
    opacity: 1;
}

/* Color Blind Friendly Indicators */
.drop-zone.correct::after {
    content: "✓";
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #4caf50;
    font-size: 1.5rem;
    font-weight: bold;
}

.drop-zone.incorrect::after {
    content: "✗";
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #f44336;
    font-size: 1.5rem;
    font-weight: bold;
}
