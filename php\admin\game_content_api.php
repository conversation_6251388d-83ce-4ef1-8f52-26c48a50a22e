<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../dbconnection.php';

class GameContentAPI {
    private $conn;
    
    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }
    
    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $action = $_GET['action'] ?? '';
        
        try {
            switch ($method) {
                case 'GET':
                    $this->handleGet($action);
                    break;
                case 'POST':
                    $this->handlePost($action);
                    break;
                case 'PUT':
                    $this->handlePut($action);
                    break;
                case 'DELETE':
                    $this->handleDelete($action);
                    break;
                default:
                    $this->sendResponse(['error' => 'Method not allowed'], 405);
            }
        } catch (Exception $e) {
            $this->sendResponse(['error' => $e->getMessage()], 500);
        }
    }
    
    private function handleGet($action) {
        switch ($action) {
            case 'get_all_content':
                $this->getAllContent();
                break;
            case 'get_content_by_level':
                $level = $_GET['level'] ?? 0;
                $this->getContentByLevel($level);
                break;
            case 'get_levels':
                $this->getLevels();
                break;
            default:
                $this->sendResponse(['error' => 'Invalid action'], 400);
        }
    }
    
    private function handlePost($action) {
        // Handle both FormData and JSON input for POST requests
        $input = null;
        $contentType = $_SERVER['CONTENT_TYPE'] ?? '';

        if (strpos($contentType, 'application/json') !== false) {
            $input = json_decode(file_get_contents('php://input'), true);
        } else {
            // Handle FormData (multipart/form-data or application/x-www-form-urlencoded)
            $input = $_POST;
        }

        if ($input === null) {
            $this->sendResponse(['error' => 'Invalid input data'], 400);
            return;
        }

        switch ($action) {
            case 'add_content':
                $this->addContent($input);
                break;
            default:
                $this->sendResponse(['error' => 'Invalid action'], 400);
        }
    }
    
    private function handlePut($action) {
        $input = json_decode(file_get_contents('php://input'), true);

        if ($input === null) {
            $this->sendResponse(['error' => 'Invalid JSON input for PUT request'], 400);
            return;
        }

        switch ($action) {
            case 'update_content':
                $this->updateContent($input);
                break;
            default:
                $this->sendResponse(['error' => 'Invalid action'], 400);
        }
    }
    
    private function handleDelete($action) {
        switch ($action) {
            case 'delete_content':
                $contentId = $_GET['content_id'] ?? 0;
                $this->deleteContent($contentId);
                break;
            default:
                $this->sendResponse(['error' => 'Invalid action'], 400);
        }
    }
    
    private function getAllContent() {
        $query = "SELECT * FROM game_content ORDER BY level_number, content_id";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        
        $content = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $this->sendResponse(['success' => true, 'data' => $content]);
    }
    
    private function getContentByLevel($level) {
        $query = "SELECT * FROM game_content WHERE level_number = ? ORDER BY content_id";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$level]);
        
        $content = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $this->sendResponse(['success' => true, 'data' => $content]);
    }
    
    private function getLevels() {
        $query = "SELECT DISTINCT level_number, COUNT(*) as question_count 
                  FROM game_content 
                  GROUP BY level_number 
                  ORDER BY level_number";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        
        $levels = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $this->sendResponse(['success' => true, 'data' => $levels]);
    }
    
    private function addContent($data) {
        $query = "INSERT INTO game_content (level_number, question_text, option1, option2, option3, option4, correct_answer) 
                  VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $this->conn->prepare($query);
        $result = $stmt->execute([
            $data['level_number'],
            $data['question_text'],
            $data['option1'],
            $data['option2'],
            $data['option3'],
            $data['option4'],
            $data['correct_answer']
        ]);
        
        if ($result) {
            $contentId = $this->conn->lastInsertId();
            $this->sendResponse(['success' => true, 'content_id' => $contentId, 'message' => 'Content added successfully']);
        } else {
            $this->sendResponse(['success' => false, 'error' => 'Failed to add content'], 500);
        }
    }
    
    private function updateContent($data) {
        // Validate required fields
        $requiredFields = ['content_id', 'level_number', 'question_text', 'option1', 'option2', 'option3', 'option4', 'correct_answer'];
        foreach ($requiredFields as $field) {
            if (!isset($data[$field])) {
                $this->sendResponse(['success' => false, 'error' => "Missing required field: $field"], 400);
                return;
            }
        }

        $query = "UPDATE game_content
                  SET level_number = ?, question_text = ?, option1 = ?, option2 = ?, option3 = ?, option4 = ?, correct_answer = ?
                  WHERE content_id = ?";

        try {
            $stmt = $this->conn->prepare($query);
            $result = $stmt->execute([
                $data['level_number'],
                $data['question_text'],
                $data['option1'],
                $data['option2'],
                $data['option3'],
                $data['option4'],
                $data['correct_answer'],
                $data['content_id']
            ]);

            if ($result) {
                $rowsAffected = $stmt->rowCount();
                if ($rowsAffected > 0) {
                    $this->sendResponse(['success' => true, 'message' => 'Content updated successfully']);
                } else {
                    $this->sendResponse(['success' => false, 'error' => 'No content found with the specified ID'], 404);
                }
            } else {
                $this->sendResponse(['success' => false, 'error' => 'Failed to execute update query'], 500);
            }
        } catch (PDOException $e) {
            $this->sendResponse(['success' => false, 'error' => 'Database error: ' . $e->getMessage()], 500);
        }
    }
    
    private function deleteContent($contentId) {
        $query = "DELETE FROM game_content WHERE content_id = ?";
        $stmt = $this->conn->prepare($query);
        $result = $stmt->execute([$contentId]);
        
        if ($result) {
            $this->sendResponse(['success' => true, 'message' => 'Content deleted successfully']);
        } else {
            $this->sendResponse(['success' => false, 'error' => 'Failed to delete content'], 500);
        }
    }
    
    private function sendResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        echo json_encode($data);
        exit;
    }
}

// Initialize and handle the request
$api = new GameContentAPI();
$api->handleRequest();
