function toggle_open_profile() {
    document.getElementById("toggle_profile").style.width = "250px";
}

function toggle_close_profile() {
    document.getElementById("toggle_profile").style.width = "0%";
    // <!-- <span>${levelNumber}</span> -->
}

function logout() {
    // Send a request to the server to destroy the session
    fetch('../php/logout.php', {
        method: 'POST',
        credentials: 'same-origin' // Include cookies in the request
    })
    .then(response => {
        if (response.ok) {
            // Redirect to login page or home page after successful logout
            window.location.href = '../html/login.html';
        } else {
            console.error('Logout failed');
        }
    })
    .catch(error => {
        console.error('Error during logout:', error);
    });
}

// // Function to check and update achievements
// async function updateAchievements() {
//     try {
//         const response = await fetch('../php/update_achievements.php', {
//             method: 'POST',
//             credentials: 'include',
//             headers: {
//                 'Content-Type': 'application/json'
//             }
//         });

//         const data = await response.json();
        
//         if (!response.ok) {
//             throw new Error(data.message || 'Failed to update achievements');
//         }

//         console.log('Achievements update result:', data);
        
//         // Show notification if achievements were unlocked
//         if (data.updated_achievements && data.updated_achievements.length > 0) {
//             showAchievementUnlocked(data.updated_achievements.length);
//         }

//         return data;
//     } catch (error) {
//         console.error('Error updating achievements:', error);
//         return {
//             success: false,
//             error: error.message
//         };
//     }
// }

// Function to show unlocked achievements notification
function showAchievementUnlocked(count) {
    const notification = document.createElement('div');
    notification.className = 'achievement-notification';
    notification.innerHTML = `
        <div class="notification-content">
            <div class="achievement-icon">
                <span class="trophy">🏆</span>
                <div class="sparkles">
                    <span class="sparkle">✨</span>
                    <span class="sparkle">✨</span>
                    <span class="sparkle">✨</span>
                </div>
            </div>
            <div class="achievement-text">
                <h3>New Achievement${count > 1 ? 's' : ''} Unlocked!</h3>
                <p>You've earned ${count} new achievement${count > 1 ? 's' : ''}</p>
            </div>
            <button class="view-achievements-btn">View Achievements</button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Add click handler for the view achievements button
    const viewBtn = notification.querySelector('.view-achievements-btn');
    viewBtn.addEventListener('click', () => {
        // Redirect to achievements page or show achievements modal
        window.location.href = 'achievements.html';
    });
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        notification.classList.add('fade-out');
        setTimeout(() => notification.remove(), 500);
    }, 5000);
}

// Function to update achievement progress
async function updateAchievementProgress() {
    try {
        const response = await fetch('../php/update_achievements_progress.php', {
            method: 'POST',
            credentials: 'include',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || 'Failed to update achievement progress');
        }

        console.log('Achievement progress update result:', data);
        return data;
    } catch (error) {
        console.error('Error updating achievement progress:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

async function fetchAndDisplayTier() {
    try {
        // First, fetch the user's current exp
        console.log("Fetching user exp...");
        const userResponse = await fetch('../php/get_user_exp.php');
        if (!userResponse.ok) throw new Error(`HTTP error! status: ${userResponse.status}`);

        const userData = await userResponse.json();
        console.log("Fetched user data:", userData);

        if (!userData.success || userData.userExp === undefined) {
            throw new Error('Failed to fetch user exp: ' + (userData.message || 'No exp data received'));
        }

        const userExp = parseInt(userData.userExp);
        if (isNaN(userExp)) throw new Error('Invalid exp value received');

        console.log("User exp:", userExp);

        // Then fetch the tier table
        console.log("Fetching tier table...");
        const tierResponse = await fetch('../php/get_tier_table.php');
        if (!tierResponse.ok) throw new Error(`HTTP error! status: ${tierResponse.status}`);

        const tierData = await tierResponse.json();
        console.log("Fetched tier data:", tierData);

        if (!tierData.success || !tierData.tier) {
            throw new Error('Failed to fetch tier table: ' + (tierData.message || 'No tiers data received'));
        }

        // Determine the user's tier based on their exp
        let userTier = "Beginner"; // Default tier name
        const sortedTiers = tierData.tier.sort((a, b) => parseInt(a.expNeeded) - parseInt(b.expNeeded));
        console.log("Sorted tiers:", sortedTiers);

        for (const tier of sortedTiers) {
            const expNeeded = parseInt(tier.expNeeded);
            if (userExp >= expNeeded) {
                userTier = tier.expName;
            } else {
                break;
            }
        }

        console.log(`Calculated tier - Exp: ${userExp}, Tier: ${userTier}`);
        document.getElementById('user_tier').textContent = userTier;

    } catch (error) {
        console.error('Error in tier calculation:', error);
        document.getElementById('user_tier').textContent = 'Beginner';
    }
}

// Function to lock levels and display stars
async function lockLevels() {
    const levelsContainer = document.querySelector('.cont-levels-row');
    if (!levelsContainer) {
        console.error('Levels container not found');
        return;
    }

    let unlockedLevels = 0;
    let levelsData = {};

    try {
        const response = await fetch('../php/get_unlocked_levels.php');
        if (!response.ok) throw new Error('Network error');
        const data = await response.json();
        unlockedLevels = data.unlockedLevels;
        levelsData = data.levelsData || {};
        console.log("Fetched unlocked levels:", unlockedLevels, "with data:", levelsData);
    } catch (error) {
        console.error('Error fetching unlocked levels:', error);
        // Fallback to localStorage or default value
        unlockedLevels = localStorage.getItem('unlockedLevels') || 1;
    }

    const levelButtons = levelsContainer.querySelectorAll('.cont-levels-column button, .level-button');
    
    levelButtons.forEach((button, index) => {
        const levelNumber = index + 1;
        const levelStars = levelsData[levelNumber] || 0;
        
        console.log("Checking level", levelNumber, "against unlocked:", unlockedLevels, "with stars:", levelStars);
        
        if (levelNumber > unlockedLevels) {
            // Lock the level
            button.classList.add('locked');
            button.innerHTML = `
                <div class="locked-level">
                    <img src="../images/lock.png" alt="Locked">
                    <!-- <span>${levelNumber}</span> -->
                </div>
            `;
            button.onclick = function(e) {
                e.preventDefault();
                alert('This level is locked. Complete previous levels to unlock.');
            };
        } else {
            // Unlock the level
            button.classList.remove('locked');
            button.innerHTML = `<div class="cont-levels-column">
                <span><a href="game/game${levelNumber}.html?level=${levelNumber}">${levelNumber}</a></span>
                ${levelStars > 0 ? 
                    `<div class="level-stars">
                        ${'⭐'.repeat(levelStars)}${'✰'.repeat(3-levelStars)}
                    </div>
                    </div>` : 
                    `<div class="level-stars">
                        ${'⭐'.repeat(levelStars)}${'✰'.repeat(3-levelStars)}
                    </div>`
                }
            `;
        }
    });
}

// Function to fetch and display user levels
async function fetchAndDisplayLevels() {
    try {
        const response = await fetch('../php/get_user_levels.php', {
            method: 'GET',
            credentials: 'include'
        });

        if (!response.ok) {
            throw new Error('Failed to fetch user levels');
        }

        const data = await response.json();
        
        if (data.success) {
            displayLevels(data.levels);
        } else {
            console.error('Error:', data.error);
        }
    } catch (error) {
        console.error('Error fetching levels:', error);
    }
}

// Function to display levels
function displayLevels(levels) {
    const levelsContainer = document.querySelector('.cont-levels-row');
    levelsContainer.innerHTML = ''; // Clear existing levels

    levels.forEach(level => {
        const levelColumn = document.createElement('div');
        levelColumn.className = 'cont-levels-column';

        const button = document.createElement('button');
        button.className = level.isUnlocked ? 'unlocked' : 'locked';
        
        // Create level number span
        const levelNumber = document.createElement('span');
        levelNumber.textContent = level.level_number;
        button.appendChild(levelNumber);

        // Add stars if level is completed
        if (level.levelStar) {
            const stars = document.createElement('span');
            stars.className = 'level-stars';
            stars.textContent = '⭐'.repeat(level.levelStar);
            button.appendChild(stars);
        }

        // Add score if level is completed
        if (level.levelScore) {
            const score = document.createElement('span');
            score.className = 'level-score';
            score.textContent = level.levelScore;
            button.appendChild(score);
        }

        // Add click handler for unlocked levels
        if (level.isUnlocked) {
            button.onclick = () => {
                window.location.href = `game/game${level.level_number}.html`;
            };
        }

        levelColumn.appendChild(button);
        levelsContainer.appendChild(levelColumn);
    });
}

// Function to load user profile data
async function loadUserProfile() {
    try {
        const response = await fetch('../php/get_profile.php');
        if (!response.ok) {
            throw new Error('Failed to fetch profile');
        }

        const data = await response.json();
        if (data.success) {
            // Update profile avatar in toggle menu
            const profileAvatar = document.getElementById('profile-avatar');
            if (data.avatar && profileAvatar) {
                profileAvatar.src = data.avatar;
            }

            // Update main profile icon
            const mainProfileIcon = document.getElementById('main-profile-icon');
            if (data.avatar && mainProfileIcon) {
                mainProfileIcon.src = data.avatar;
            }
        }
    } catch (error) {
        console.error('Error loading profile:', error);
    }
}

// Function to load user ID
async function loadUserId() {
    try {
        // Since we need the session userId, we'll make a simple request to get it
        const response = await fetch('../php/get_user_session.php');
        if (response.ok) {
            const data = await response.json();
            if (data.success && data.userId) {
                const userIdDisplay = document.getElementById('user-id-display');
                if (userIdDisplay) {
                    userIdDisplay.textContent = `UID: ${data.userId}`;
                }
            } else {
                // User not logged in or error
                const userIdDisplay = document.getElementById('user-id-display');
                if (userIdDisplay) {
                    userIdDisplay.textContent = 'UID: Not logged in';
                }
            }
        }
    } catch (error) {
        console.error('Error loading user ID:', error);
        // Fallback - show error message
        const userIdDisplay = document.getElementById('user-id-display');
        if (userIdDisplay) {
            userIdDisplay.textContent = 'UID: Error loading';
        }
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Load user profile and ID
    loadUserProfile();
    loadUserId();

    // Update achievements and their progress
        // updateAchievements();
    updateAchievementProgress();

    // Fetch and display tier
    fetchAndDisplayTier().catch(error => {
        console.error('Error in fetchAndDisplayTier:', error);
    });

    // Ensure all DOM elements are loaded first
    setTimeout(() => {
        lockLevels().catch(error => {
            console.error('Error in lockLevels:', error);
        });
    }, 100);

    // Fetch and display user levels
    fetchAndDisplayLevels();
});